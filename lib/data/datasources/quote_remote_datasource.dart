import 'dart:math';
import '../../core/errors/exceptions.dart';
import '../../core/network/api_client.dart';
import '../models/quote_model.dart';

abstract class QuoteRemoteDataSource {
  Future<QuoteModel> getRandomQuote(List<String> categories);
  Future<List<QuoteModel>> getQuotesByCategory(String category);
  Future<List<String>> getAvailableCategories();
  Future<List<QuoteModel>> searchQuotes(String query);
}

class QuoteRemoteDataSourceImpl implements QuoteRemoteDataSource {
  final ApiClient apiClient;

  QuoteRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<QuoteModel> getRandomQuote(List<String> categories) async {
    try {
      // For demo purposes, we'll use a fallback quote system
      // In production, you'd call a real API
      final fallbackQuotes = _getFallbackQuotes();
      final categoryQuotes = categories.isEmpty
          ? fallbackQuotes
          : fallbackQuotes.where((quote) => categories.contains(quote.category)).toList();

      if (categoryQuotes.isEmpty) {
        throw ServerException(
          message: 'No quotes found for selected categories',
          code: 404,
        );
      }

      final random = Random();
      final randomQuote = categoryQuotes[random.nextInt(categoryQuotes.length)];
      return randomQuote;
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException(
        message: 'Failed to fetch random quote: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<List<QuoteModel>> getQuotesByCategory(String category) async {
    try {
      final fallbackQuotes = _getFallbackQuotes();
      final categoryQuotes =
          fallbackQuotes.where((quote) => quote.category.toLowerCase() == category.toLowerCase()).toList();

      return categoryQuotes;
    } catch (e) {
      throw ServerException(
        message: 'Failed to fetch quotes by category: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<List<String>> getAvailableCategories() async {
    try {
      final fallbackQuotes = _getFallbackQuotes();
      final categories = fallbackQuotes.map((quote) => quote.category).toSet().toList();

      return categories;
    } catch (e) {
      throw ServerException(
        message: 'Failed to fetch available categories: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<List<QuoteModel>> searchQuotes(String query) async {
    try {
      final fallbackQuotes = _getFallbackQuotes();
      final searchResults = fallbackQuotes
          .where((quote) =>
              quote.text.toLowerCase().contains(query.toLowerCase()) ||
              quote.author.toLowerCase().contains(query.toLowerCase()))
          .toList();

      return searchResults;
    } catch (e) {
      throw ServerException(
        message: 'Failed to search quotes: ${e.toString()}',
        code: 500,
      );
    }
  }

  List<QuoteModel> _getFallbackQuotes() {
    return [
      QuoteModel.create(
        text: "The only way to do great work is to love what you do.",
        author: "Steve Jobs",
        category: "success",
        tags: ["work", "passion"],
      ),
      QuoteModel.create(
        text: "Success is not final, failure is not fatal: it is the courage to continue that counts.",
        author: "Winston Churchill",
        category: "courage",
        tags: ["perseverance", "resilience"],
      ),
      QuoteModel.create(
        text: "The future belongs to those who believe in the beauty of their dreams.",
        author: "Eleanor Roosevelt",
        category: "inspiration",
        tags: ["dreams", "future"],
      ),
      QuoteModel.create(
        text: "It is during our darkest moments that we must focus to see the light.",
        author: "Aristotle",
        category: "motivation",
        tags: ["hope", "perseverance"],
      ),
      QuoteModel.create(
        text: "Happiness is not something ready made. It comes from your own actions.",
        author: "Dalai Lama",
        category: "happiness",
        tags: ["action", "mindfulness"],
      ),
      QuoteModel.create(
        text: "The only true wisdom is in knowing you know nothing.",
        author: "Socrates",
        category: "wisdom",
        tags: ["knowledge", "humility"],
      ),
      QuoteModel.create(
        text: "Love yourself first and everything else falls into line.",
        author: "Lucille Ball",
        category: "love",
        tags: ["self-love", "confidence"],
      ),
      QuoteModel.create(
        text: "A leader is one who knows the way, goes the way, and shows the way.",
        author: "John C. Maxwell",
        category: "leadership",
        tags: ["guidance", "example"],
      ),
      QuoteModel.create(
        text: "Growth begins at the end of your comfort zone.",
        author: "Neale Donald Walsch",
        category: "growth",
        tags: ["comfort zone", "development"],
      ),
      QuoteModel.create(
        text: "The present moment is the only time over which we have dominion.",
        author: "Thich Nhat Hanh",
        category: "mindfulness",
        tags: ["present", "awareness"],
      ),
    ];
  }
}
