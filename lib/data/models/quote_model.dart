import '../../domain/entities/quote.dart';

class QuoteModel extends Quote {
  const QuoteModel({
    required String id,
    required String text,
    required String author,
    required String category,
    required DateTime createdAt,
    bool isFavorite = false,
    List<String> tags = const [],
  }) : super(
          id: id,
          text: text,
          author: author,
          category: category,
          createdAt: createdAt,
          isFavorite: isFavorite,
          tags: tags,
        );

  factory QuoteModel.fromJson(Map<String, dynamic> json) {
    return QuoteModel(
      id: json['id']?.toString() ?? '',
      text: json['text']?.toString() ?? json['content']?.toString() ?? '',
      author: json['author']?.toString() ?? 'Unknown',
      category: json['category']?.toString() ?? 'general',
      createdAt: json['createdAt'] != null
          ? DateTime.tryParse(json['createdAt'].toString()) ?? DateTime.now()
          : DateTime.now(),
      isFavorite: json['isFavorite'] == true,
      tags: json['tags'] != null
          ? List<String>.from(json['tags'].map((x) => x.toString()))
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'author': author,
      'category': category,
      'createdAt': createdAt.toIso8601String(),
      'isFavorite': isFavorite,
      'tags': tags,
    };
  }

  factory QuoteModel.fromEntity(Quote quote) {
    return QuoteModel(
      id: quote.id,
      text: quote.text,
      author: quote.author,
      category: quote.category,
      createdAt: quote.createdAt,
      isFavorite: quote.isFavorite,
      tags: quote.tags,
    );
  }

  QuoteModel copyWith({
    String? id,
    String? text,
    String? author,
    String? category,
    DateTime? createdAt,
    bool? isFavorite,
    List<String>? tags,
  }) {
    return QuoteModel(
      id: id ?? this.id,
      text: text ?? this.text,
      author: author ?? this.author,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      isFavorite: isFavorite ?? this.isFavorite,
      tags: tags ?? this.tags,
    );
  }

  // Factory for creating a quote with generated ID
  factory QuoteModel.create({
    required String text,
    required String author,
    required String category,
    List<String> tags = const [],
  }) {
    return QuoteModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      text: text,
      author: author,
      category: category,
      createdAt: DateTime.now(),
      tags: tags,
    );
  }
}
