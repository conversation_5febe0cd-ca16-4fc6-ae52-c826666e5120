import 'package:flutter/material.dart';
import '../../domain/entities/user_preferences.dart';

class UserPreferencesModel extends UserPreferences {
  const UserPreferencesModel({
    List<String> selectedCategories = const [],
    bool notificationEnabled = true,
    List<TimeOfDay> notificationTimes = const [TimeOfDay(hour: 9, minute: 0)],
    int notificationCount = 3,
    bool onboardingComplete = false,
    String preferredLanguage = 'en',
    bool darkModeEnabled = false,
    required DateTime lastQuoteDate,
    List<String> favoriteQuoteIds = const [],
  }) : super(
          selectedCategories: selectedCategories,
          notificationEnabled: notificationEnabled,
          notificationTimes: notificationTimes,
          notificationCount: notificationCount,
          onboardingComplete: onboardingComplete,
          preferredLanguage: preferredLanguage,
          darkModeEnabled: darkModeEnabled,
          lastQuoteDate: lastQuoteDate,
          favoriteQuoteIds: favoriteQuoteIds,
        );

  factory UserPreferencesModel.fromEntity(UserPreferences preferences) {
    return UserPreferencesModel(
      selectedCategories: preferences.selectedCategories,
      notificationEnabled: preferences.notificationEnabled,
      notificationTimes: preferences.notificationTimes,
      notificationCount: preferences.notificationCount,
      onboardingComplete: preferences.onboardingComplete,
      preferredLanguage: preferences.preferredLanguage,
      darkModeEnabled: preferences.darkModeEnabled,
      lastQuoteDate: preferences.lastQuoteDate,
      favoriteQuoteIds: preferences.favoriteQuoteIds,
    );
  }

  UserPreferencesModel copyWith({
    List<String>? selectedCategories,
    bool? notificationEnabled,
    List<TimeOfDay>? notificationTimes,
    int? notificationCount,
    bool? onboardingComplete,
    String? preferredLanguage,
    bool? darkModeEnabled,
    DateTime? lastQuoteDate,
    List<String>? favoriteQuoteIds,
  }) {
    return UserPreferencesModel(
      selectedCategories: selectedCategories ?? this.selectedCategories,
      notificationEnabled: notificationEnabled ?? this.notificationEnabled,
      notificationTimes: notificationTimes ?? this.notificationTimes,
      notificationCount: notificationCount ?? this.notificationCount,
      onboardingComplete: onboardingComplete ?? this.onboardingComplete,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      darkModeEnabled: darkModeEnabled ?? this.darkModeEnabled,
      lastQuoteDate: lastQuoteDate ?? this.lastQuoteDate,
      favoriteQuoteIds: favoriteQuoteIds ?? this.favoriteQuoteIds,
    );
  }

  // Factory for creating default preferences
  factory UserPreferencesModel.defaultPreferences() {
    return UserPreferencesModel(
      selectedCategories: ['motivation', 'success', 'inspiration'],
      notificationEnabled: true,
      notificationTimes: const [
        TimeOfDay(hour: 9, minute: 0),
        TimeOfDay(hour: 14, minute: 0),
        TimeOfDay(hour: 18, minute: 0),
      ],
      notificationCount: 3,
      onboardingComplete: false,
      preferredLanguage: 'en',
      darkModeEnabled: false,
      lastQuoteDate: DateTime.now(),
      favoriteQuoteIds: [],
    );
  }
}
