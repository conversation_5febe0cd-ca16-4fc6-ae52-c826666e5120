import 'package:equatable/equatable.dart';

class Quote extends Equatable {
  final String id;
  final String text;
  final String author;
  final String category;
  final DateTime createdAt;
  final bool isFavorite;
  final List<String> tags;

  const Quote({
    required this.id,
    required this.text,
    required this.author,
    required this.category,
    required this.createdAt,
    this.isFavorite = false,
    this.tags = const [],
  });

  Quote copyWith({
    String? id,
    String? text,
    String? author,
    String? category,
    DateTime? createdAt,
    bool? isFavorite,
    List<String>? tags,
  }) {
    return Quote(
      id: id ?? this.id,
      text: text ?? this.text,
      author: author ?? this.author,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      isFavorite: isFavorite ?? this.isFavorite,
      tags: tags ?? this.tags,
    );
  }

  @override
  List<Object?> get props => [
        id,
        text,
        author,
        category,
        createdAt,
        isFavorite,
        tags,
      ];

  @override
  String toString() {
    return 'Quote(id: $id, text: $text, author: $author, category: $category, createdAt: $createdAt, isFavorite: $isFavorite, tags: $tags)';
  }
}
