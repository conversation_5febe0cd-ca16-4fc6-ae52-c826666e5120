import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../providers/preferences_provider.dart';
import '../providers/notification_provider.dart';
import '../widgets/category_selection_widget.dart';
import '../widgets/notification_count_widget.dart';
import '../widgets/time_selection_widget.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isUpdatingNotifications = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        centerTitle: true,
      ),
      body: Consumer2<PreferencesProvider, NotificationProvider>(
        builder: (context, preferencesProvider, notificationProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              _buildCategoriesSection(),
              const SizedBox(height: 24),
              _buildNotificationSection(),
              const SizedBox(height: 24),
              _buildTimeSection(),
              const SizedBox(height: 24),
              _buildUpdateNotificationsButton(preferencesProvider, notificationProvider),
              const SizedBox(height: 32),
              _buildAboutSection(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Quote Categories',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Select the types of quotes you want to receive:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 16),
            const CategorySelectionWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Daily Notifications',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choose how many motivational quotes you want per day:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 16),
            const NotificationCountWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Times',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Set specific times or let us choose random times for you:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 16),
            const TimeSelectionWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildUpdateNotificationsButton(
    PreferencesProvider preferencesProvider,
    NotificationProvider notificationProvider,
  ) {
    return SizedBox(
      width: double.infinity,
      child: FilledButton.icon(
        onPressed: _isUpdatingNotifications
            ? null
            : () => _updateNotifications(preferencesProvider, notificationProvider),
        icon: _isUpdatingNotifications
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.notifications_active),
        label: Text(_isUpdatingNotifications ? 'Updating...' : 'Update Notifications'),
      ),
    );
  }

  Future<void> _updateNotifications(
    PreferencesProvider preferencesProvider,
    NotificationProvider notificationProvider,
  ) async {
    setState(() {
      _isUpdatingNotifications = true;
    });

    try {
      if (!notificationProvider.permissionsGranted) {
        final granted = await notificationProvider.requestPermissions();
        if (!granted) {
          throw Exception('Notification permissions are required');
        }
      }

      await notificationProvider.scheduleNotifications(
        preferencesProvider.notificationTimes,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notifications updated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update notifications: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isUpdatingNotifications = false;
      });
    }
  }

  Widget _buildAboutSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'About',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.info_outline),
              title: const Text('Version'),
              subtitle: const Text('1.0.0'),
              contentPadding: EdgeInsets.zero,
            ),
            ListTile(
              leading: const Icon(Icons.api),
              title: const Text('Quote Source'),
              subtitle: const Text('ZenQuotes.io'),
              contentPadding: EdgeInsets.zero,
              onTap: () => _launchUrl('https://zenquotes.io'),
            ),
            ListTile(
              leading: const Icon(Icons.code),
              title: const Text('Open Source'),
              subtitle: const Text('Built with Flutter'),
              contentPadding: EdgeInsets.zero,
            ),
            ListTile(
              leading: const Icon(Icons.star_rate),
              title: const Text('Rate This App'),
              subtitle: const Text('Help us improve'),
              contentPadding: EdgeInsets.zero,
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Thank you for your support!'),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }
}
