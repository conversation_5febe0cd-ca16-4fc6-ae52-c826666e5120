import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/services/image_service.dart';
import '../../core/utils/image_generator.dart';
import '../../core/theme/app_theme.dart';

class ImageShowcaseScreen extends StatefulWidget {
  const ImageShowcaseScreen({super.key});

  @override
  State<ImageShowcaseScreen> createState() => _ImageShowcaseScreenState();
}

class _ImageShowcaseScreenState extends State<ImageShowcaseScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final customColors = theme.customColors;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Image Showcase'),
        backgroundColor: customColors.primaryGradient.first,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Categories', icon: Icon(Icons.category)),
            Tab(text: 'Achievements', icon: Icon(Icons.emoji_events)),
            Tab(text: 'Backgrounds', icon: Icon(Icons.wallpaper)),
            Tab(text: 'Avatars', icon: Icon(Icons.person)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCategoriesTab(),
          _buildAchievementsTab(),
          _buildBackgroundsTab(),
          _buildAvatarsTab(),
        ],
      ),
    );
  }

  Widget _buildCategoriesTab() {
    const categories = [
      'motivation', 'success', 'life', 'wisdom', 'happiness',
      'inspiration', 'leadership', 'growth', 'mindfulness', 'courage'
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Category Images',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Generated category images with unique patterns and colors',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              return _buildCategoryCard(category);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(String category) {
    return Card(
      elevation: 4,
      child: Column(
        children: [
          Expanded(
            child: ImageService.buildCategoryIcon(
              category: category,
              size: double.infinity,
              fallbackColor: Colors.blue,
              fallbackIcon: Icons.category,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8),
            child: Text(
              category.toUpperCase(),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    )
    .animate()
    .fadeIn(delay: (100 * (category.hashCode % 10)).ms)
    .scale(delay: (100 * (category.hashCode % 10)).ms);
  }

  Widget _buildAchievementsTab() {
    const achievements = [
      'first_quote', 'streak_3', 'streak_7', 'streak_30',
      'category_explorer', 'quote_master', 'early_bird', 'night_owl',
      'weekend_warrior', 'perfect_week'
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Achievement Badges',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Unlocked and locked achievement badges with unique designs',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          
          // Unlocked achievements
          Text(
            'Unlocked Achievements',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          const SizedBox(height: 12),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              childAspectRatio: 1,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: achievements.length ~/ 2,
            itemBuilder: (context, index) {
              final achievement = achievements[index];
              return _buildAchievementBadge(achievement, true);
            },
          ),
          
          const SizedBox(height: 24),
          
          // Locked achievements
          Text(
            'Locked Achievements',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 12),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              childAspectRatio: 1,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: achievements.length - (achievements.length ~/ 2),
            itemBuilder: (context, index) {
              final achievement = achievements[index + (achievements.length ~/ 2)];
              return _buildAchievementBadge(achievement, false);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementBadge(String achievement, bool isUnlocked) {
    return ImageService.buildAchievementBadge(
      achievementKey: achievement,
      size: 60,
      fallbackColor: Colors.blue,
      fallbackIcon: Icons.emoji_events,
      isUnlocked: isUnlocked,
    )
    .animate()
    .scale(delay: (50 * achievement.hashCode % 500).ms)
    .shimmer(delay: (200 + achievement.hashCode % 300).ms, duration: 1000.ms);
  }

  Widget _buildBackgroundsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Background Patterns',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Generated background patterns for quote cards',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: 8,
            itemBuilder: (context, index) {
              return _buildBackgroundCard(index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBackgroundCard(int index) {
    return Card(
      elevation: 4,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            ImageGenerator.generateBackgroundImage(
              index: index,
              width: double.infinity,
              height: double.infinity,
              borderRadius: BorderRadius.circular(12),
            ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.3),
                  ],
                ),
              ),
            ),
            Positioned(
              bottom: 8,
              left: 8,
              child: Text(
                'Pattern ${index + 1}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    )
    .animate()
    .fadeIn(delay: (100 * index).ms)
    .slideY(begin: 0.3, delay: (100 * index).ms);
  }

  Widget _buildAvatarsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'User Avatars',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Level-based avatar designs with unique patterns',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              childAspectRatio: 1,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: 12,
            itemBuilder: (context, index) {
              return _buildAvatarCard(index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAvatarCard(int index) {
    final level = (index + 1) * 5;
    return Column(
      children: [
        Expanded(
          child: ImageService.buildAvatar(
            userLevel: level,
            size: 80,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Level $level',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
      ],
    )
    .animate()
    .scale(delay: (50 * index).ms)
    .fadeIn(delay: (50 * index).ms);
  }
}
