import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quote_provider.dart';
import '../providers/preferences_provider.dart';
import '../widgets/quote_card_widget.dart';
import 'settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchInitialQuote();
    });
  }

  Future<void> _fetchInitialQuote() async {
    final quoteProvider = Provider.of<QuoteProvider>(context, listen: false);
    final preferencesProvider = Provider.of<PreferencesProvider>(context, listen: false);
    await quoteProvider.fetchRandomQuote(preferencesProvider.selectedCategories);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Daily Motivator'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const SettingsScreen()),
              );
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          final quoteProvider = Provider.of<QuoteProvider>(context, listen: false);
          final preferencesProvider = Provider.of<PreferencesProvider>(context, listen: false);
          await quoteProvider.fetchRandomQuote(preferencesProvider.selectedCategories);
        },
        child: Consumer<QuoteProvider>(
          builder: (context, quoteProvider, child) {
            return SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildGreeting(),
                  const SizedBox(height: 24),
                  _buildQuoteSection(quoteProvider),
                  const SizedBox(height: 32),
                  _buildActionButtons(quoteProvider),
                  const SizedBox(height: 24),
                  _buildCategoriesInfo(),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildGreeting() {
    final hour = DateTime.now().hour;
    String greeting;

    if (hour < 12) {
      greeting = 'Good Morning!';
    } else if (hour < 17) {
      greeting = 'Good Afternoon!';
    } else {
      greeting = 'Good Evening!';
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              greeting,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 4),
            Text(
              'Ready for your daily dose of motivation?',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuoteSection(QuoteProvider quoteProvider) {
    switch (quoteProvider.status) {
      case QuoteStatus.loading:
        return const Card(
          child: Padding(
            padding: EdgeInsets.all(32.0),
            child: Center(
              child: CircularProgressIndicator(),
            ),
          ),
        );

      case QuoteStatus.loaded:
        if (quoteProvider.currentQuote != null) {
          return QuoteCardWidget(quote: quoteProvider.currentQuote!);
        }
        return _buildErrorCard('No quote available');

      case QuoteStatus.error:
        return _buildErrorCard(
            quoteProvider.errorMessage.isNotEmpty ? quoteProvider.errorMessage : 'Failed to load quote');

      case QuoteStatus.initial:
      default:
        return const Card(
          child: Padding(
            padding: EdgeInsets.all(32.0),
            child: Center(
              child: Text('Tap "New Quote" to get started!'),
            ),
          ),
        );
    }
  }

  Widget _buildErrorCard(String message) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              message,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(QuoteProvider quoteProvider) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: FilledButton.icon(
            onPressed: quoteProvider.status == QuoteStatus.loading
                ? null
                : () {
                    final preferencesProvider = Provider.of<PreferencesProvider>(context, listen: false);
                    quoteProvider.fetchRandomQuote(preferencesProvider.selectedCategories);
                  },
            icon: const Icon(Icons.refresh),
            label: const Text('New Quote'),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const SettingsScreen()),
              );
            },
            icon: const Icon(Icons.tune),
            label: const Text('Customize Notifications'),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoriesInfo() {
    return Consumer<PreferencesProvider>(
      builder: (context, preferencesProvider, child) {
        if (preferencesProvider.selectedCategories.isEmpty) {
          return Card(
            color: Colors.orange[50],
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.orange[700],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'No categories selected',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.orange[700],
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Go to settings to select your favorite quote categories for personalized notifications.',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.orange[600],
                        ),
                  ),
                ],
              ),
            ),
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Categories',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: preferencesProvider.selectedCategories
                      .map((category) => Chip(
                            label: Text(
                              category.substring(0, 1).toUpperCase() + category.substring(1),
                            ),
                            backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                          ))
                      .toList(),
                ),
                const SizedBox(height: 8),
                Text(
                  '${preferencesProvider.notificationCount} notification${preferencesProvider.notificationCount == 1 ? '' : 's'} per day',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
