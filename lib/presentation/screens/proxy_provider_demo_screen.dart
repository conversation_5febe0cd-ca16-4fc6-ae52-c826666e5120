import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../providers/analytics_provider.dart';
import '../providers/gamification_provider.dart';
import '../providers/recommendation_provider.dart';
import '../providers/social_provider.dart';
import '../providers/quote_provider.dart';
import '../providers/preferences_provider.dart';
import '../providers/theme_provider.dart';

class ProxyProviderDemoScreen extends StatefulWidget {
  const ProxyProviderDemoScreen({super.key});

  @override
  State<ProxyProviderDemoScreen> createState() => _ProxyProviderDemoScreenState();
}

class _ProxyProviderDemoScreenState extends State<ProxyProviderDemoScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final customColors = theme.customColors;

    return Scaffold(
      appBar: AppBar(
        title: const Text('ProxyProvider Demo'),
        backgroundColor: customColors.primaryGradient.first,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Analytics', icon: Icon(Icons.analytics)),
            Tab(text: 'Gamification', icon: Icon(Icons.emoji_events)),
            Tab(text: 'Recommendations', icon: Icon(Icons.recommend)),
            Tab(text: 'Social', icon: Icon(Icons.share)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAnalyticsTab(),
          _buildGamificationTab(),
          _buildRecommendationsTab(),
          _buildSocialTab(),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    return Consumer3<AnalyticsProvider, QuoteProvider, PreferencesProvider>(
      builder: (context, analytics, quote, preferences, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader(
                'Analytics Provider',
                'Depends on: QuoteProvider + PreferencesProvider',
                Icons.analytics,
              ),
              const SizedBox(height: 16),
              
              _buildDependencyCard(
                'Quote Provider Dependency',
                'Reacts to quote changes and tracks viewing patterns',
                [
                  'Current Quote: ${quote.currentQuote?.author ?? 'None'}',
                  'Status: ${quote.status.name}',
                  'Total Views Tracked: ${analytics.totalQuotesViewed}',
                ],
                Colors.blue,
              ),
              
              const SizedBox(height: 16),
              
              _buildDependencyCard(
                'Preferences Provider Dependency',
                'Uses selected categories to weight analytics',
                [
                  'Selected Categories: ${preferences.selectedCategories.join(', ')}',
                  'Favorite Category: ${analytics.favoriteCategory ?? 'None'}',
                  'Category Engagement: ${analytics.categoryEngagement.length} tracked',
                ],
                Colors.green,
              ),
              
              const SizedBox(height: 16),
              
              _buildStatsGrid(analytics),
            ],
          ),
        );
      },
    );
  }

  Widget _buildGamificationTab() {
    return Consumer4<GamificationProvider, AnalyticsProvider, QuoteProvider, PreferencesProvider>(
      builder: (context, gamification, analytics, quote, preferences, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader(
                'Gamification Provider',
                'Depends on: AnalyticsProvider + QuoteProvider + PreferencesProvider',
                Icons.emoji_events,
              ),
              const SizedBox(height: 16),
              
              _buildDependencyCard(
                'Analytics Provider Dependency',
                'Uses analytics data to unlock achievements',
                [
                  'Current Streak: ${analytics.currentStreak} days',
                  'Total Quotes: ${analytics.totalQuotesViewed}',
                  'Achievements Unlocked: ${gamification.achievementCount}',
                ],
                Colors.orange,
              ),
              
              const SizedBox(height: 16),
              
              _buildDependencyCard(
                'Quote Provider Dependency',
                'Awards XP for each quote read',
                [
                  'Current Level: ${gamification.level}',
                  'Experience Points: ${gamification.experiencePoints}',
                  'XP to Next Level: ${gamification.experienceToNextLevel}',
                ],
                Colors.purple,
              ),
              
              const SizedBox(height: 16),
              
              _buildAchievementsList(gamification),
            ],
          ),
        );
      },
    );
  }

  Widget _buildRecommendationsTab() {
    return Consumer3<RecommendationProvider, AnalyticsProvider, PreferencesProvider>(
      builder: (context, recommendations, analytics, preferences, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader(
                'Recommendation Provider',
                'Depends on: AnalyticsProvider + PreferencesProvider + QuoteProvider',
                Icons.recommend,
              ),
              const SizedBox(height: 16),
              
              if (recommendations.isGenerating)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(32),
                    child: Column(
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Generating personalized recommendations...'),
                      ],
                    ),
                  ),
                )
              else ...[
                _buildRecommendationCard(
                  'Recommended Categories',
                  recommendations.recommendedCategories,
                  recommendations.categoryScores,
                ),
                
                const SizedBox(height: 16),
                
                _buildPersonalizedTags(recommendations.personalizedTags),
                
                const SizedBox(height: 16),
                
                _buildInsightsCard(recommendations.motivationalInsights),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildSocialTab() {
    return Consumer4<SocialProvider, AnalyticsProvider, GamificationProvider, ThemeProvider>(
      builder: (context, social, analytics, gamification, theme, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader(
                'Social Provider',
                'Depends on: AnalyticsProvider + GamificationProvider + QuoteProvider + ThemeProvider',
                Icons.share,
              ),
              const SizedBox(height: 16),
              
              _buildSocialProfile(social.socialProfile),
              
              const SizedBox(height: 16),
              
              _buildSocialChallenges(social.activeChallenges),
              
              const SizedBox(height: 16),
              
              _buildShareStats(social.shareStats),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title, String subtitle, IconData icon) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(icon, size: 32, color: theme.colorScheme.primary),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDependencyCard(String title, String description, List<String> details, Color color) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 4,
                  height: 20,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 12),
            ...details.map((detail) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  Icon(Icons.arrow_right, size: 16, color: color),
                  const SizedBox(width: 8),
                  Expanded(child: Text(detail)),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsGrid(AnalyticsProvider analytics) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard('Current Streak', '${analytics.currentStreak}', Icons.local_fire_department),
        _buildStatCard('Total Quotes', '${analytics.totalQuotesViewed}', Icons.library_books),
        _buildStatCard('Categories', '${analytics.categoryStats.length}', Icons.category),
        _buildStatCard('Engagement', '${analytics.engagementScore.toStringAsFixed(1)}', Icons.trending_up),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 24, color: theme.colorScheme.primary),
            const SizedBox(height: 8),
            Text(
              value,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            Text(
              title,
              style: theme.textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementsList(GamificationProvider gamification) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Achievements',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...gamification.recentlyUnlocked.take(3).map((achievement) => 
              ListTile(
                leading: Icon(achievement.icon, color: achievement.color),
                title: Text(achievement.title),
                subtitle: Text(achievement.description),
                trailing: Text('${achievement.points} pts'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationCard(String title, List<String> categories, Map<String, double> scores) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...categories.take(3).map((category) => 
              Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Expanded(child: Text(category)),
                    Text('${((scores[category] ?? 0) * 100).round()}%'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalizedTags(List<String> tags) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Your Tags',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children: tags.map((tag) => Chip(label: Text(tag))).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInsightsCard(Map<String, String> insights) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Motivational Insights',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...insights.entries.map((entry) => 
              Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text('• ${entry.value}'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialProfile(Map<String, dynamic> profile) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Social Profile',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text('Display Name: ${profile['displayName'] ?? 'Unknown'}'),
            Text('Level: ${profile['level'] ?? 0}'),
            Text('Motto: ${profile['motto'] ?? 'No motto set'}'),
            const SizedBox(height: 8),
            if (profile['badges'] != null)
              Wrap(
                spacing: 4,
                children: (profile['badges'] as List<String>)
                    .map((badge) => Chip(label: Text(badge, style: const TextStyle(fontSize: 12))))
                    .toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialChallenges(List<SocialChallenge> challenges) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Active Challenges',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...challenges.take(2).map((challenge) => 
              Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(challenge.title, style: const TextStyle(fontWeight: FontWeight.w500)),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(value: challenge.progressPercentage),
                    const SizedBox(height: 4),
                    Text('${challenge.progress}/${challenge.target}'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShareStats(Map<String, int> shareStats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Share Statistics',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text('Total Shares: ${shareStats['total'] ?? 0}'),
            ...shareStats.entries
                .where((entry) => entry.key != 'total')
                .take(3)
                .map((entry) => Text('${entry.key}: ${entry.value}')),
          ],
        ),
      ),
    );
  }
}
