import 'package:flutter/material.dart';
import '../../core/services/notification_service.dart';
import '../../domain/usecases/get_random_quote.dart';

class NotificationProvider extends ChangeNotifier {
  final NotificationService notificationService;
  final GetRandomQuote getRandomQuote;

  bool _permissionsGranted = false;
  bool _isScheduling = false;

  NotificationProvider({
    required this.notificationService,
    required this.getRandomQuote,
  });

  bool get permissionsGranted => _permissionsGranted;
  bool get isScheduling => _isScheduling;

  Future<void> initialize() async {
    await notificationService.initialize();
  }

  Future<bool> requestPermissions() async {
    _permissionsGranted = await notificationService.requestPermissions();
    notifyListeners();
    return _permissionsGranted;
  }

  Future<void> scheduleNotifications(List<TimeOfDay> times) async {
    if (!_permissionsGranted) {
      throw Exception('Notification permissions not granted');
    }

    _isScheduling = true;
    notifyListeners();

    try {
      // Schedule notifications with categories
      await notificationService.scheduleQuoteNotifications(
        times: times,
        categories: ['motivation', 'success', 'inspiration'], // Default categories
      );
    } catch (e) {
      throw Exception('Failed to schedule notifications: $e');
    } finally {
      _isScheduling = false;
      notifyListeners();
    }
  }
}
