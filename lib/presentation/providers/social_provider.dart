import 'package:flutter/material.dart';
import '../../domain/entities/quote.dart';
import 'analytics_provider.dart';
import 'gamification_provider.dart';
import 'quote_provider.dart';
import 'theme_provider.dart';

/// SocialProvider - Second ProxyProvider Example
/// Depends on: AnalyticsProvider, GamificationProvider, QuoteProvider, ThemeProvider
/// Demonstrates: Social features that react to user behavior and achievements
class SocialProvider extends ChangeNotifier {
  final AnalyticsProvider _analyticsProvider;
  final GamificationProvider _gamificationProvider;
  final QuoteProvider _quoteProvider;
  final ThemeProvider _themeProvider;

  SocialProvider({
    required AnalyticsProvider analyticsProvider,
    required GamificationProvider gamificationProvider,
    required QuoteProvider quoteProvider,
    required ThemeProvider themeProvider,
  })  : _analyticsProvider = analyticsProvider,
        _gamificationProvider = gamificationProvider,
        _quoteProvider = quoteProvider,
        _themeProvider = themeProvider {
    // Listen to all dependencies
    _analyticsProvider.addListener(_onAnalyticsChanged);
    _gamificationProvider.addListener(_onGamificationChanged);
    _quoteProvider.addListener(_onQuoteChanged);
    _themeProvider.addListener(_onThemeChanged);
    _initializeSocialFeatures();
  }

  // Social state
  List<ShareableQuote> _sharedQuotes = [];
  Map<String, int> _shareStats = {};
  List<Achievement> _shareableAchievements = [];
  List<SocialChallenge> _activeChallenges = [];
  Map<String, dynamic> _socialProfile = {};
  List<String> _motivationalMessages = [];
  int _socialScore = 0;
  bool _isPublicProfile = false;
  DateTime? _lastShareTime;

  // Getters
  List<ShareableQuote> get sharedQuotes => List.unmodifiable(_sharedQuotes);
  Map<String, int> get shareStats => Map.unmodifiable(_shareStats);
  List<Achievement> get shareableAchievements => List.unmodifiable(_shareableAchievements);
  List<SocialChallenge> get activeChallenges => List.unmodifiable(_activeChallenges);
  Map<String, dynamic> get socialProfile => Map.unmodifiable(_socialProfile);
  List<String> get motivationalMessages => List.unmodifiable(_motivationalMessages);
  int get socialScore => _socialScore;
  bool get isPublicProfile => _isPublicProfile;
  DateTime? get lastShareTime => _lastShareTime;

  // React to analytics changes
  void _onAnalyticsChanged() {
    _updateSocialProfile();
    _generateMotivationalMessages();
    _updateSocialChallenges();
  }

  // React to gamification changes
  void _onGamificationChanged() {
    _updateShareableAchievements();
    _calculateSocialScore();
    _checkForSocialMilestones();
  }

  // React to quote changes
  void _onQuoteChanged() {
    if (_quoteProvider.currentQuote != null) {
      _createShareableQuote(_quoteProvider.currentQuote!);
    }
  }

  // React to theme changes
  void _onThemeChanged() {
    _updateQuoteVisualStyles();
  }

  // Initialize social features
  void _initializeSocialFeatures() {
    _updateSocialProfile();
    _generateMotivationalMessages();
    _initializeChallenges();
    _calculateSocialScore();
  }

  // Update social profile based on user behavior
  void _updateSocialProfile() {
    final streak = _analyticsProvider.currentStreak;
    final totalQuotes = _analyticsProvider.totalQuotesViewed;
    final favoriteCategory = _analyticsProvider.favoriteCategory;
    final level = _gamificationProvider.level;

    _socialProfile = {
      'displayName': _generateDisplayName(),
      'level': level,
      'streak': streak,
      'totalQuotes': totalQuotes,
      'favoriteCategory': favoriteCategory,
      'joinDate': DateTime.now().subtract(Duration(days: totalQuotes)),
      'badges': _generateBadges(),
      'motto': _generatePersonalMotto(),
      'stats': {
        'quotesShared': _shareStats['total'] ?? 0,
        'achievementsUnlocked': _gamificationProvider.achievementCount,
        'daysActive': _analyticsProvider.dailyQuoteCount.length,
        'engagementScore': _analyticsProvider.engagementScore,
      }
    };
    
    notifyListeners();
  }

  // Generate display name based on achievements
  String _generateDisplayName() {
    final level = _gamificationProvider.level;
    final streak = _analyticsProvider.currentStreak;
    
    if (streak >= 30) {
      return 'Motivation Master';
    } else if (level >= 10) {
      return 'Wisdom Seeker';
    } else if (streak >= 7) {
      return 'Streak Champion';
    } else if (_analyticsProvider.totalQuotesViewed >= 50) {
      return 'Quote Explorer';
    } else {
      return 'Inspiration Seeker';
    }
  }

  // Generate badges based on achievements
  List<String> _generateBadges() {
    final badges = <String>[];
    final achievements = _gamificationProvider.unlockedAchievements;
    
    for (final achievement in achievements) {
      switch (achievement.type) {
        case AchievementType.streak7:
          badges.add('🔥 Week Warrior');
          break;
        case AchievementType.streak30:
          badges.add('👑 Streak Master');
          break;
        case AchievementType.quoteMaster:
          badges.add('📚 Quote Master');
          break;
        case AchievementType.categoryExplorer:
          badges.add('🗺️ Explorer');
          break;
        case AchievementType.earlyBird:
          badges.add('🌅 Early Bird');
          break;
        case AchievementType.nightOwl:
          badges.add('🌙 Night Owl');
          break;
        default:
          break;
      }
    }
    
    // Social-specific badges
    final shareCount = _shareStats['total'] ?? 0;
    if (shareCount >= 10) {
      badges.add('📢 Social Motivator');
    }
    if (shareCount >= 50) {
      badges.add('🌟 Inspiration Spreader');
    }
    
    return badges;
  }

  // Generate personal motto
  String _generatePersonalMotto() {
    final favoriteCategory = _analyticsProvider.favoriteCategory;
    final streak = _analyticsProvider.currentStreak;
    
    if (streak >= 30) {
      return 'Consistency is my superpower';
    } else if (favoriteCategory == 'motivation') {
      return 'Driven by motivation, powered by action';
    } else if (favoriteCategory == 'wisdom') {
      return 'Seeking wisdom, sharing insights';
    } else if (favoriteCategory == 'success') {
      return 'Success is a journey, not a destination';
    } else {
      return 'Growing stronger with every quote';
    }
  }

  // Create shareable quote with visual styling
  void _createShareableQuote(Quote quote) {
    final shareableQuote = ShareableQuote(
      quote: quote,
      backgroundColor: _getThemeBasedBackground(),
      textColor: _getThemeBasedTextColor(),
      authorLevel: _gamificationProvider.level,
      userBadges: _generateBadges(),
      shareCount: 0,
      createdAt: DateTime.now(),
    );
    
    _sharedQuotes.insert(0, shareableQuote);
    
    // Keep only last 20 shared quotes
    if (_sharedQuotes.length > 20) {
      _sharedQuotes = _sharedQuotes.take(20).toList();
    }
    
    notifyListeners();
  }

  // Get theme-based background color
  String _getThemeBasedBackground() {
    // This would use theme colors in a real implementation
    return _themeProvider.isDarkMode(navigatorKey.currentContext!) 
        ? '#1a1a1a' 
        : '#ffffff';
  }

  // Get theme-based text color
  String _getThemeBasedTextColor() {
    return _themeProvider.isDarkMode(navigatorKey.currentContext!) 
        ? '#ffffff' 
        : '#000000';
  }

  // Update visual styles when theme changes
  void _updateQuoteVisualStyles() {
    for (int i = 0; i < _sharedQuotes.length; i++) {
      _sharedQuotes[i] = _sharedQuotes[i].copyWith(
        backgroundColor: _getThemeBasedBackground(),
        textColor: _getThemeBasedTextColor(),
      );
    }
    notifyListeners();
  }

  // Update shareable achievements
  void _updateShareableAchievements() {
    _shareableAchievements = _gamificationProvider.unlockedAchievements
        .where((achievement) => achievement.isUnlocked)
        .toList();
    notifyListeners();
  }

  // Calculate social score
  void _calculateSocialScore() {
    int score = 0;
    
    // Base score from level
    score += _gamificationProvider.level * 10;
    
    // Streak bonus
    score += _analyticsProvider.currentStreak * 5;
    
    // Achievement bonus
    score += _gamificationProvider.achievementCount * 15;
    
    // Share bonus
    score += (_shareStats['total'] ?? 0) * 3;
    
    // Engagement bonus
    score += (_analyticsProvider.engagementScore * 10).round();
    
    _socialScore = score;
    notifyListeners();
  }

  // Generate motivational messages for social sharing
  void _generateMotivationalMessages() {
    _motivationalMessages.clear();
    
    final streak = _analyticsProvider.currentStreak;
    final level = _gamificationProvider.level;
    final totalQuotes = _analyticsProvider.totalQuotesViewed;
    
    if (streak >= 30) {
      _motivationalMessages.add('🔥 30+ day streak! I\'m building unstoppable habits with Daily Motivator!');
    }
    
    if (level >= 10) {
      _motivationalMessages.add('🌟 Level $level achieved! Growing stronger with every quote!');
    }
    
    if (totalQuotes >= 100) {
      _motivationalMessages.add('📚 $totalQuotes quotes read! Building my library of wisdom!');
    }
    
    if (_gamificationProvider.hasNewAchievements) {
      _motivationalMessages.add('🏆 New achievement unlocked! Celebrating progress!');
    }
    
    // Daily motivation
    if (_analyticsProvider.hasViewedQuotesToday) {
      _motivationalMessages.add('💪 Starting the day right with motivation!');
    }
  }

  // Initialize social challenges
  void _initializeChallenges() {
    _activeChallenges = [
      SocialChallenge(
        id: 'weekly_streak',
        title: '7-Day Streak Challenge',
        description: 'Read a quote every day for 7 days',
        progress: _analyticsProvider.currentStreak.clamp(0, 7),
        target: 7,
        reward: 'Streak Champion Badge',
        expiresAt: DateTime.now().add(const Duration(days: 7)),
      ),
      SocialChallenge(
        id: 'category_explorer',
        title: 'Category Explorer',
        description: 'Read quotes from 5 different categories',
        progress: _analyticsProvider.categoryStats.length,
        target: 5,
        reward: 'Explorer Badge',
        expiresAt: DateTime.now().add(const Duration(days: 14)),
      ),
      SocialChallenge(
        id: 'share_master',
        title: 'Share Master',
        description: 'Share 10 quotes with friends',
        progress: _shareStats['total'] ?? 0,
        target: 10,
        reward: 'Social Motivator Badge',
        expiresAt: DateTime.now().add(const Duration(days: 30)),
      ),
    ];
  }

  // Update social challenges based on progress
  void _updateSocialChallenges() {
    for (int i = 0; i < _activeChallenges.length; i++) {
      final challenge = _activeChallenges[i];
      
      switch (challenge.id) {
        case 'weekly_streak':
          _activeChallenges[i] = challenge.copyWith(
            progress: _analyticsProvider.currentStreak.clamp(0, 7),
          );
          break;
        case 'category_explorer':
          _activeChallenges[i] = challenge.copyWith(
            progress: _analyticsProvider.categoryStats.length,
          );
          break;
        case 'share_master':
          _activeChallenges[i] = challenge.copyWith(
            progress: _shareStats['total'] ?? 0,
          );
          break;
      }
    }
    notifyListeners();
  }

  // Check for social milestones
  void _checkForSocialMilestones() {
    // This would trigger celebrations for social achievements
    if (_gamificationProvider.hasNewAchievements) {
      _generateMotivationalMessages();
    }
  }

  // Share a quote
  Future<void> shareQuote(ShareableQuote shareableQuote) async {
    _shareStats['total'] = (_shareStats['total'] ?? 0) + 1;
    _shareStats[shareableQuote.quote.category] = 
        (_shareStats[shareableQuote.quote.category] ?? 0) + 1;
    
    _lastShareTime = DateTime.now();
    
    // Update the shared quote's share count
    final index = _sharedQuotes.indexOf(shareableQuote);
    if (index != -1) {
      _sharedQuotes[index] = shareableQuote.copyWith(
        shareCount: shareableQuote.shareCount + 1,
      );
    }
    
    _calculateSocialScore();
    _updateSocialChallenges();
    notifyListeners();
  }

  // Toggle public profile
  void togglePublicProfile() {
    _isPublicProfile = !_isPublicProfile;
    notifyListeners();
  }

  // Get social sharing text
  String getSocialSharingText(ShareableQuote shareableQuote) {
    final quote = shareableQuote.quote;
    final badges = shareableQuote.userBadges.take(2).join(' ');
    
    return '"${quote.text}"\n\n— ${quote.author}\n\n'
           '${badges.isNotEmpty ? '$badges ' : ''}'
           'Shared from Daily Motivator 📱\n'
           '#Motivation #Inspiration #DailyQuotes';
  }

  @override
  void dispose() {
    _analyticsProvider.removeListener(_onAnalyticsChanged);
    _gamificationProvider.removeListener(_onGamificationChanged);
    _quoteProvider.removeListener(_onQuoteChanged);
    _themeProvider.removeListener(_onThemeChanged);
    super.dispose();
  }
}

// Supporting classes
class ShareableQuote {
  final Quote quote;
  final String backgroundColor;
  final String textColor;
  final int authorLevel;
  final List<String> userBadges;
  final int shareCount;
  final DateTime createdAt;

  const ShareableQuote({
    required this.quote,
    required this.backgroundColor,
    required this.textColor,
    required this.authorLevel,
    required this.userBadges,
    required this.shareCount,
    required this.createdAt,
  });

  ShareableQuote copyWith({
    String? backgroundColor,
    String? textColor,
    int? shareCount,
  }) {
    return ShareableQuote(
      quote: quote,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      textColor: textColor ?? this.textColor,
      authorLevel: authorLevel,
      userBadges: userBadges,
      shareCount: shareCount ?? this.shareCount,
      createdAt: createdAt,
    );
  }
}

class SocialChallenge {
  final String id;
  final String title;
  final String description;
  final int progress;
  final int target;
  final String reward;
  final DateTime expiresAt;

  const SocialChallenge({
    required this.id,
    required this.title,
    required this.description,
    required this.progress,
    required this.target,
    required this.reward,
    required this.expiresAt,
  });

  bool get isCompleted => progress >= target;
  double get progressPercentage => (progress / target).clamp(0.0, 1.0);

  SocialChallenge copyWith({
    int? progress,
  }) {
    return SocialChallenge(
      id: id,
      title: title,
      description: description,
      progress: progress ?? this.progress,
      target: target,
      reward: reward,
      expiresAt: expiresAt,
    );
  }
}

// Global navigator key for theme access
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
