import 'package:flutter/material.dart';
import '../../domain/entities/quote.dart';
import '../../domain/entities/user_preferences.dart';
import 'analytics_provider.dart';
import 'preferences_provider.dart';
import 'quote_provider.dart';

/// RecommendationProvider - First ProxyProvider Example
/// Depends on: AnalyticsProvider, PreferencesProvider, QuoteProvider
/// Demonstrates: Complex multi-provider dependency with ML-like recommendations
class RecommendationProvider extends ChangeNotifier {
  final AnalyticsProvider _analyticsProvider;
  final PreferencesProvider _preferencesProvider;
  final QuoteProvider _quoteProvider;

  RecommendationProvider({
    required AnalyticsProvider analyticsProvider,
    required PreferencesProvider preferencesProvider,
    required QuoteProvider quoteProvider,
  })  : _analyticsProvider = analyticsProvider,
        _preferencesProvider = preferencesProvider,
        _quoteProvider = quoteProvider {
    // Listen to all dependencies
    _analyticsProvider.addListener(_onAnalyticsChanged);
    _preferencesProvider.addListener(_onPreferencesChanged);
    _quoteProvider.addListener(_onQuoteChanged);
    _generateRecommendations();
  }

  // Recommendation state
  List<String> _recommendedCategories = [];
  Map<String, double> _categoryScores = {};
  List<String> _personalizedTags = [];
  String? _recommendedTimeOfDay;
  Map<String, String> _motivationalInsights = {};
  bool _isGenerating = false;
  DateTime? _lastRecommendationUpdate;

  // Getters
  List<String> get recommendedCategories => List.unmodifiable(_recommendedCategories);
  Map<String, double> get categoryScores => Map.unmodifiable(_categoryScores);
  List<String> get personalizedTags => List.unmodifiable(_personalizedTags);
  String? get recommendedTimeOfDay => _recommendedTimeOfDay;
  Map<String, String> get motivationalInsights => Map.unmodifiable(_motivationalInsights);
  bool get isGenerating => _isGenerating;
  DateTime? get lastRecommendationUpdate => _lastRecommendationUpdate;

  // React to analytics changes
  void _onAnalyticsChanged() {
    _generateRecommendations();
  }

  // React to preferences changes
  void _onPreferencesChanged() {
    _generateRecommendations();
  }

  // React to quote changes
  void _onQuoteChanged() {
    if (_quoteProvider.currentQuote != null) {
      _updateRecommendationsBasedOnCurrentQuote();
    }
  }

  // Generate personalized recommendations
  Future<void> _generateRecommendations() async {
    if (_isGenerating) return;
    
    _isGenerating = true;
    notifyListeners();

    try {
      // Simulate ML processing delay
      await Future.delayed(const Duration(milliseconds: 500));

      _generateCategoryRecommendations();
      _generatePersonalizedTags();
      _generateTimeRecommendations();
      _generateMotivationalInsights();

      _lastRecommendationUpdate = DateTime.now();
    } finally {
      _isGenerating = false;
      notifyListeners();
    }
  }

  // Generate category recommendations based on user behavior
  void _generateCategoryRecommendations() {
    final categoryStats = _analyticsProvider.categoryStats;
    final selectedCategories = _preferencesProvider.selectedCategories;
    final currentStreak = _analyticsProvider.currentStreak;
    
    // Calculate scores for each category
    _categoryScores.clear();
    
    // Base categories from user preferences
    for (final category in selectedCategories) {
      _categoryScores[category] = 0.8; // High base score for selected categories
    }
    
    // Boost categories based on usage patterns
    for (final entry in categoryStats.entries) {
      final category = entry.key;
      final count = entry.value;
      
      // Calculate engagement score
      double score = _categoryScores[category] ?? 0.0;
      score += (count / _analyticsProvider.totalQuotesViewed) * 0.5;
      
      // Boost if it's a recent category
      if (_analyticsProvider.recentCategories.contains(category)) {
        score += 0.2;
      }
      
      // Streak bonus
      if (currentStreak > 7) {
        score += 0.1; // Consistency bonus
      }
      
      _categoryScores[category] = score.clamp(0.0, 1.0);
    }
    
    // Sort and get top recommendations
    final sortedCategories = _categoryScores.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    _recommendedCategories = sortedCategories
        .take(5)
        .map((e) => e.key)
        .toList();
    
    // Add some variety if user is too focused on one category
    _addVarietyRecommendations();
  }

  // Add variety to prevent category tunnel vision
  void _addVarietyRecommendations() {
    final allCategories = [
      'motivation', 'success', 'life', 'wisdom', 'happiness',
      'inspiration', 'leadership', 'growth', 'mindfulness', 'courage'
    ];
    
    final unexploredCategories = allCategories
        .where((cat) => !_analyticsProvider.categoryStats.containsKey(cat))
        .toList();
    
    if (unexploredCategories.isNotEmpty && _recommendedCategories.length < 5) {
      // Add one unexplored category for variety
      _recommendedCategories.add(unexploredCategories.first);
      _categoryScores[unexploredCategories.first] = 0.3; // Discovery score
    }
  }

  // Generate personalized tags based on behavior
  void _generatePersonalizedTags() {
    _personalizedTags.clear();
    
    final currentStreak = _analyticsProvider.currentStreak;
    final totalQuotes = _analyticsProvider.totalQuotesViewed;
    final favoriteCategory = _analyticsProvider.favoriteCategory;
    
    // Streak-based tags
    if (currentStreak >= 30) {
      _personalizedTags.add('Consistency Master');
    } else if (currentStreak >= 7) {
      _personalizedTags.add('Streak Builder');
    } else if (currentStreak >= 3) {
      _personalizedTags.add('Getting Started');
    }
    
    // Volume-based tags
    if (totalQuotes >= 100) {
      _personalizedTags.add('Quote Collector');
    } else if (totalQuotes >= 50) {
      _personalizedTags.add('Motivation Seeker');
    } else if (totalQuotes >= 10) {
      _personalizedTags.add('Explorer');
    }
    
    // Category-based tags
    if (favoriteCategory != null) {
      _personalizedTags.add('${favoriteCategory.substring(0, 1).toUpperCase()}${favoriteCategory.substring(1)} Enthusiast');
    }
    
    // Engagement-based tags
    final engagementScore = _analyticsProvider.engagementScore;
    if (engagementScore >= 8.0) {
      _personalizedTags.add('Highly Engaged');
    } else if (engagementScore >= 5.0) {
      _personalizedTags.add('Regular Reader');
    }
    
    // Time-based tags
    if (_analyticsProvider.hasViewedQuotesToday) {
      _personalizedTags.add('Daily Motivator');
    }
  }

  // Generate optimal time recommendations
  void _generateTimeRecommendations() {
    final dailyCount = _analyticsProvider.dailyQuoteCount;
    final currentStreak = _analyticsProvider.currentStreak;
    
    // Analyze user's reading patterns (simplified)
    if (currentStreak >= 7) {
      // For consistent users, recommend maintaining their pattern
      if (_analyticsProvider.quotesTodayCount == 0) {
        final hour = DateTime.now().hour;
        if (hour < 10) {
          _recommendedTimeOfDay = 'Morning (8-10 AM)';
        } else if (hour < 14) {
          _recommendedTimeOfDay = 'Lunch Break (12-2 PM)';
        } else if (hour < 18) {
          _recommendedTimeOfDay = 'Afternoon (3-5 PM)';
        } else {
          _recommendedTimeOfDay = 'Evening (7-9 PM)';
        }
      }
    } else {
      // For new users, recommend morning motivation
      _recommendedTimeOfDay = 'Morning (8-9 AM)';
    }
  }

  // Generate motivational insights
  void _generateMotivationalInsights() {
    _motivationalInsights.clear();
    
    final currentStreak = _analyticsProvider.currentStreak;
    final totalQuotes = _analyticsProvider.totalQuotesViewed;
    final favoriteCategory = _analyticsProvider.favoriteCategory;
    
    // Streak insights
    if (currentStreak >= 30) {
      _motivationalInsights['streak'] = 'Your 30+ day streak shows incredible dedication! You\'re building a powerful habit.';
    } else if (currentStreak >= 7) {
      _motivationalInsights['streak'] = 'A week-long streak is fantastic! You\'re on your way to making this a lasting habit.';
    } else if (currentStreak >= 3) {
      _motivationalInsights['streak'] = 'Three days in a row! You\'re building momentum. Keep it up!';
    }
    
    // Category insights
    if (favoriteCategory != null) {
      _motivationalInsights['category'] = 'You seem drawn to $favoriteCategory quotes. This suggests you value ${_getCategoryInsight(favoriteCategory)}.';
    }
    
    // Progress insights
    if (totalQuotes >= 50) {
      _motivationalInsights['progress'] = 'You\'ve read $totalQuotes quotes! That\'s a library of wisdom you\'ve absorbed.';
    }
    
    // Personalized recommendations
    if (_recommendedCategories.isNotEmpty) {
      _motivationalInsights['recommendation'] = 'Based on your reading patterns, you might enjoy exploring ${_recommendedCategories.first} quotes next.';
    }
  }

  // Get insight for category
  String _getCategoryInsight(String category) {
    switch (category.toLowerCase()) {
      case 'motivation':
        return 'drive and determination';
      case 'success':
        return 'achievement and excellence';
      case 'wisdom':
        return 'knowledge and understanding';
      case 'happiness':
        return 'joy and positivity';
      case 'leadership':
        return 'guidance and influence';
      default:
        return 'personal growth';
    }
  }

  // Update recommendations based on current quote
  void _updateRecommendationsBasedOnCurrentQuote() {
    final currentQuote = _quoteProvider.currentQuote;
    if (currentQuote == null) return;
    
    // Boost the category of the current quote
    final category = currentQuote.category;
    if (_categoryScores.containsKey(category)) {
      _categoryScores[category] = (_categoryScores[category]! + 0.1).clamp(0.0, 1.0);
    } else {
      _categoryScores[category] = 0.4;
    }
    
    // Re-sort recommendations
    final sortedCategories = _categoryScores.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    _recommendedCategories = sortedCategories
        .take(5)
        .map((e) => e.key)
        .toList();
    
    notifyListeners();
  }

  // Get recommendation strength (0-100)
  int getRecommendationStrength(String category) {
    final score = _categoryScores[category] ?? 0.0;
    return (score * 100).round();
  }

  // Get next recommended action
  String getNextRecommendedAction() {
    if (_analyticsProvider.quotesTodayCount == 0) {
      return 'Start your day with a motivational quote!';
    } else if (_analyticsProvider.currentStreak == 0) {
      return 'Begin building your reading streak today!';
    } else if (_recommendedCategories.isNotEmpty) {
      return 'Try exploring ${_recommendedCategories.first} quotes';
    } else {
      return 'Keep up your great reading habit!';
    }
  }

  // Force refresh recommendations
  Future<void> refreshRecommendations() async {
    await _generateRecommendations();
  }

  @override
  void dispose() {
    _analyticsProvider.removeListener(_onAnalyticsChanged);
    _preferencesProvider.removeListener(_onPreferencesChanged);
    _quoteProvider.removeListener(_onQuoteChanged);
    super.dispose();
  }
}
