import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:confetti/confetti.dart';
import 'package:share_plus/share_plus.dart';
import '../../domain/entities/quote.dart';
import '../../core/theme/app_theme.dart';
import '../../core/services/image_service.dart';

class QuoteCardWidget extends StatefulWidget {
  final Quote quote;
  final VoidCallback? onFavorite;
  final VoidCallback? onShare;
  final bool isFavorite;

  const QuoteCardWidget({
    super.key,
    required this.quote,
    this.onFavorite,
    this.onShare,
    this.isFavorite = false,
  });

  @override
  State<QuoteCardWidget> createState() => _QuoteCardWidgetState();
}

class _QuoteCardWidgetState extends State<QuoteCardWidget>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _rotationController;
  late AnimationController _favoriteController;
  late ConfettiController _confettiController;

  bool _isPressed = false;
  bool _showActions = false;

  @override
  void initState() {
    super.initState();

    _scaleController = AnimationController(
      duration: AppTheme.fastAnimation,
      vsync: this,
    );

    _rotationController = AnimationController(
      duration: AppTheme.normalAnimation,
      vsync: this,
    );

    _favoriteController = AnimationController(
      duration: AppTheme.normalAnimation,
      vsync: this,
    );

    _confettiController = ConfettiController(
      duration: const Duration(milliseconds: 500),
    );

    // Start entrance animation
    Future.delayed(const Duration(milliseconds: 100), () {
      _scaleController.forward();
    });
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _rotationController.dispose();
    _favoriteController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final customColors = theme.customColors;

    return GestureDetector(
      onTapDown: (_) => _onTapDown(),
      onTapUp: (_) => _onTapUp(),
      onTapCancel: () => _onTapUp(),
      onLongPress: () => _toggleActions(),
      child: Stack(
        children: [
          // Confetti overlay
          Align(
            alignment: Alignment.topCenter,
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirection: 3.14 / 2, // Down
              particleDrag: 0.05,
              emissionFrequency: 0.05,
              numberOfParticles: 20,
              gravity: 0.05,
              shouldLoop: false,
              colors: customColors.motivationalGradient,
            ),
          ),

          // Main card
          AnimatedBuilder(
            animation: Listenable.merge([_scaleController, _rotationController]),
            builder: (context, child) {
              return Transform.scale(
                scale: _isPressed ? 0.95 : (0.8 + (0.2 * _scaleController.value)),
                child: Transform.rotate(
                  angle: _rotationController.value * 0.02,
                  child: Card(
                    elevation: _isPressed ? 2 : 8,
                    shadowColor: customColors.primaryGradient.first.withOpacity(0.3),
                    child: ImageService.buildQuoteBackground(
                      backgroundIndex: widget.quote.category.hashCode.abs() % 7,
                      gradientColors: [
                        customColors.primaryGradient.first.withOpacity(0.8),
                        customColors.secondaryGradient.first.withOpacity(0.8),
                      ],
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildHeader(),
                              const SizedBox(height: 20),
                              _buildQuoteText(),
                              const SizedBox(height: 20),
                              _buildFooter(),
                              if (_showActions) ...[
                                const SizedBox(height: 16),
                                _buildActionButtons(),
                              ],
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    final theme = Theme.of(context);

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            Icons.format_quote,
            size: 24,
            color: theme.colorScheme.primary,
          ),
        )
        .animate()
        .scale(delay: 200.ms, duration: 400.ms)
        .shimmer(delay: 600.ms, duration: 1000.ms),

        const Spacer(),

        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: theme.colorScheme.secondaryContainer,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            widget.quote.category.toUpperCase(),
            style: theme.textTheme.labelSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSecondaryContainer,
              letterSpacing: 1,
            ),
          ),
        )
        .animate()
        .fadeIn(delay: 400.ms)
        .slideX(begin: 0.3, delay: 400.ms),
      ],
    );
  }

  Widget _buildQuoteText() {
    final theme = Theme.of(context);

    return Text(
      '"${widget.quote.text}"',
      style: theme.textTheme.headlineSmall?.copyWith(
        fontStyle: FontStyle.italic,
        fontWeight: FontWeight.w500,
        height: 1.5,
        color: theme.colorScheme.onSurface,
      ),
    )
    .animate()
    .fadeIn(delay: 600.ms, duration: 800.ms)
    .slideY(begin: 0.2, delay: 600.ms, duration: 800.ms);
  }

  Widget _buildFooter() {
    final theme = Theme.of(context);

    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '— ${widget.quote.author}',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              )
              .animate()
              .fadeIn(delay: 800.ms)
              .slideX(begin: -0.3, delay: 800.ms),

              const SizedBox(height: 4),

              Text(
                _formatDate(widget.quote.createdAt),
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
              )
              .animate()
              .fadeIn(delay: 1000.ms),
            ],
          ),
        ),

        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildQuickActionButton(
              icon: widget.isFavorite ? Icons.favorite : Icons.favorite_border,
              color: widget.isFavorite ? Colors.red : null,
              onTap: _onFavoriteTap,
            ),
            const SizedBox(width: 8),
            _buildQuickActionButton(
              icon: Icons.share,
              onTap: _onShareTap,
            ),
            const SizedBox(width: 8),
            _buildQuickActionButton(
              icon: Icons.copy,
              onTap: _onCopyTap,
            ),
          ],
        )
        .animate()
        .fadeIn(delay: 1200.ms)
        .scale(delay: 1200.ms),
      ],
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    Color? color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.5),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            icon,
            size: 20,
            color: color ?? theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildActionButton(
              icon: Icons.favorite,
              label: 'Favorite',
              onTap: _onFavoriteTap,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildActionButton(
              icon: Icons.share,
              label: 'Share',
              onTap: _onShareTap,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildActionButton(
              icon: Icons.copy,
              label: 'Copy',
              onTap: _onCopyTap,
            ),
          ),
        ],
      ),
    )
    .animate()
    .fadeIn(duration: 300.ms)
    .slideY(begin: 0.3, duration: 300.ms);
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          child: Column(
            children: [
              Icon(
                icon,
                size: 24,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Animation and interaction methods
  void _onTapDown() {
    setState(() {
      _isPressed = true;
    });
    HapticFeedback.lightImpact();
  }

  void _onTapUp() {
    setState(() {
      _isPressed = false;
    });
  }

  void _toggleActions() {
    setState(() {
      _showActions = !_showActions;
    });
    HapticFeedback.mediumImpact();

    if (_showActions) {
      _rotationController.forward().then((_) {
        _rotationController.reverse();
      });
    }
  }

  void _onFavoriteTap() {
    HapticFeedback.lightImpact();
    _favoriteController.forward().then((_) {
      _favoriteController.reverse();
    });

    if (!widget.isFavorite) {
      _confettiController.play();
    }

    widget.onFavorite?.call();
  }

  void _onShareTap() {
    HapticFeedback.lightImpact();
    final quoteText = '"${widget.quote.text}"\n\n— ${widget.quote.author}';
    Share.share(quoteText);
    widget.onShare?.call();
  }

  void _onCopyTap() {
    HapticFeedback.lightImpact();
    final quoteText = '"${widget.quote.text}"\n\n— ${widget.quote.author}';
    Clipboard.setData(ClipboardData(text: quoteText));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Quote copied to clipboard!'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
