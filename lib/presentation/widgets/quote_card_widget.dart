import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../domain/entities/quote.dart';

class QuoteCardWidget extends StatelessWidget {
  final Quote quote;

  const QuoteCardWidget({
    super.key,
    required this.quote,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primaryContainer,
              Theme.of(context).colorScheme.secondaryContainer,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Quote icon
              Icon(
                Icons.format_quote,
                size: 32,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 16),
              
              // Quote text
              Text(
                quote.text,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontStyle: FontStyle.italic,
                  fontWeight: FontWeight.w500,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 20),
              
              // Author and actions row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '— ${quote.author}',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        if (quote.category.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Chip(
                              label: Text(
                                quote.category.substring(0, 1).toUpperCase() + 
                                quote.category.substring(1),
                                style: const TextStyle(fontSize: 12),
                              ),
                              backgroundColor: Theme.of(context).colorScheme.surface,
                              side: BorderSide(
                                color: Theme.of(context).colorScheme.outline,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  
                  // Action buttons
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        onPressed: () => _shareQuote(context),
                        icon: const Icon(Icons.share),
                        tooltip: 'Share Quote',
                      ),
                      IconButton(
                        onPressed: () => _copyQuote(context),
                        icon: const Icon(Icons.copy),
                        tooltip: 'Copy Quote',
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _shareQuote(BuildContext context) {
    // In a real app, you would use share_plus package
    _copyQuote(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Quote copied to clipboard! You can now share it.'),
      ),
    );
  }

  void _copyQuote(BuildContext context) {
    final text = '"${quote.text}" — ${quote.author}';
    Clipboard.setData(ClipboardData(text: text));
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Quote copied to clipboard!'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
