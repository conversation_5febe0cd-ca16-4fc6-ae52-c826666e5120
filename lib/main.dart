import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:flutter_native_timezone/flutter_native_timezone.dart';

import 'core/di/injection_container.dart';
import 'core/theme/app_theme.dart';
import 'presentation/providers/preferences_provider.dart';
import 'presentation/providers/theme_provider.dart';
import 'presentation/providers/analytics_provider.dart';
import 'presentation/providers/gamification_provider.dart';
import 'presentation/providers/recommendation_provider.dart';
import 'presentation/providers/social_provider.dart';
import 'presentation/screens/splash_screen.dart';
import 'presentation/screens/onboarding_screen.dart';
import 'presentation/screens/home_screen.dart';
import 'presentation/widgets/animated_loading_widget.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize timezone database
  tz.initializeTimeZones();
  final String timeZoneName = await FlutterNativeTimezone.getLocalTimezone();
  tz.setLocalLocation(tz.getLocation(timeZoneName));

  // Initialize dependency injection
  final di = InjectionContainer();
  await di.init();

  runApp(DailyMotivatorApp(di: di));
}

class DailyMotivatorApp extends StatelessWidget {
  final InjectionContainer di;

  const DailyMotivatorApp({super.key, required this.di});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Base providers
        ChangeNotifierProvider.value(value: di.quoteProvider),
        ChangeNotifierProvider.value(value: di.preferencesProvider),
        ChangeNotifierProvider.value(value: di.notificationProvider),
        ChangeNotifierProvider.value(value: di.themeProvider),

        // ProxyProvider-style providers that depend on other providers
        // This demonstrates how providers can depend on multiple other providers
        ChangeNotifierProvider.value(value: di.analyticsProvider),
        ChangeNotifierProvider.value(value: di.gamificationProvider),

        // Additional ProxyProvider examples showing complex dependencies
        ChangeNotifierProvider.value(value: di.recommendationProvider),
        ChangeNotifierProvider.value(value: di.socialProvider),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'Daily Motivator',
            debugShowCheckedModeBanner: false,
            theme: themeProvider.lightTheme,
            darkTheme: themeProvider.darkTheme,
            themeMode: themeProvider.materialThemeMode,
            home: const SplashScreen(),
          );
        },
      ),
    );
  }
}

class AppRouter extends StatelessWidget {
  const AppRouter({super.key});
 
  @override
  Widget build(BuildContext context) {
    return Consumer<PreferencesProvider>(
      builder: (context, preferencesProvider, child) {
        if (preferencesProvider.isLoading) {
          return const Scaffold(
            body: OnboardingLoadingWidget(
              message: 'Loading your preferences...',
            ),
          );
        }

        if (preferencesProvider.onboardingComplete) {
          return const HomeScreen();
        } else {
          return const OnboardingScreen();
        }
      },
    );
  }
}
