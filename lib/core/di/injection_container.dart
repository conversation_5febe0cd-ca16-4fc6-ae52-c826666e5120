import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/network/api_client.dart';
import '../../core/services/notification_service.dart';
import '../../data/datasources/preferences_local_datasource.dart';
import '../../data/datasources/quote_remote_datasource.dart';
import '../../data/repositories/preferences_repository_impl.dart';
import '../../data/repositories/quote_repository_impl.dart';
import '../../domain/repositories/preferences_repository.dart';
import '../../domain/repositories/quote_repository.dart';
import '../../domain/usecases/get_random_quote.dart';
import '../../domain/usecases/get_user_preferences.dart';
import '../../domain/usecases/save_user_preferences.dart';
import '../../presentation/providers/notification_provider.dart';
import '../../presentation/providers/preferences_provider.dart';
import '../../presentation/providers/quote_provider.dart';

class InjectionContainer {
  static final InjectionContainer _instance = InjectionContainer._internal();
  factory InjectionContainer() => _instance;
  InjectionContainer._internal();

  // Core
  late final http.Client httpClient;
  late final ApiClient apiClient;
  late final NotificationService notificationService;
  late final SharedPreferences sharedPreferences;

  // Data sources
  late final QuoteRemoteDataSource quoteRemoteDataSource;
  late final PreferencesLocalDataSource preferencesLocalDataSource;

  // Repositories
  late final QuoteRepository quoteRepository;
  late final PreferencesRepository preferencesRepository;

  // Use cases
  late final GetRandomQuote getRandomQuote;
  late final GetUserPreferences getUserPreferences;
  late final SaveUserPreferences saveUserPreferences;

  // Providers
  late final QuoteProvider quoteProvider;
  late final PreferencesProvider preferencesProvider;
  late final NotificationProvider notificationProvider;

  Future<void> init() async {
    // Core
    httpClient = http.Client();
    apiClient = ApiClient(client: httpClient);
    notificationService = NotificationService();
    sharedPreferences = await SharedPreferences.getInstance();

    // Initialize notification service
    await notificationService.initialize();

    // Data sources
    quoteRemoteDataSource = QuoteRemoteDataSourceImpl(apiClient: apiClient);
    preferencesLocalDataSource = PreferencesLocalDataSourceImpl(
      sharedPreferences: sharedPreferences,
    );

    // Repositories
    quoteRepository = QuoteRepositoryImpl(remoteDataSource: quoteRemoteDataSource);
    preferencesRepository = PreferencesRepositoryImpl(
      localDataSource: preferencesLocalDataSource,
    );

    // Use cases
    getRandomQuote = GetRandomQuote(quoteRepository);
    getUserPreferences = GetUserPreferences(preferencesRepository);
    saveUserPreferences = SaveUserPreferences(preferencesRepository);

    // Providers
    quoteProvider = QuoteProvider(getRandomQuote: getRandomQuote);
    preferencesProvider = PreferencesProvider(
      getUserPreferences: getUserPreferences,
      saveUserPreferences: saveUserPreferences,
    );
    notificationProvider = NotificationProvider(
      notificationService: notificationService,
      getRandomQuote: getRandomQuote,
    );

    // Load initial preferences
    await preferencesProvider.loadPreferences();
  }

  void dispose() {
    httpClient.close();
  }
}
