import 'dart:io';
import 'package:flutter/material.dart';

/// Helper class for Linux notification troubleshooting and setup
class LinuxNotificationHelper {
  
  /// Check if notification daemon is available on Linux
  static Future<bool> isNotificationDaemonAvailable() async {
    if (!Platform.isLinux) return true;
    
    try {
      // Check for common notification daemons
      final daemons = ['dunst', 'mako', 'notification-daemon', 'notify-osd'];
      
      for (final daemon in daemons) {
        final result = await Process.run('which', [daemon]);
        if (result.exitCode == 0) {
          return true;
        }
      }
      
      // Check if desktop environment notifications are available
      final desktopResult = await Process.run('pgrep', ['-f', 'notification']);
      if (desktopResult.exitCode == 0) {
        return true;
      }
      
      return false;
    } catch (e) {
      return false;
    }
  }
  
  /// Get notification daemon status and recommendations
  static Future<NotificationStatus> getNotificationStatus() async {
    if (!Platform.isLinux) {
      return NotificationStatus(
        isAvailable: true,
        daemon: 'System',
        message: 'Notifications supported on this platform',
      );
    }
    
    try {
      // Check for specific daemons
      final daemonChecks = {
        'dunst': 'Dunst notification daemon',
        'mako': 'Mako notification daemon (Wayland)',
        'notification-daemon': 'GNOME notification daemon',
        'notify-osd': 'Ubuntu notification daemon',
      };
      
      for (final entry in daemonChecks.entries) {
        final result = await Process.run('pgrep', ['-f', entry.key]);
        if (result.exitCode == 0) {
          return NotificationStatus(
            isAvailable: true,
            daemon: entry.value,
            message: 'Notifications are working with ${entry.value}',
          );
        }
      }
      
      // Check desktop environment
      final desktop = Platform.environment['XDG_CURRENT_DESKTOP'] ?? 
                     Platform.environment['DESKTOP_SESSION'] ?? 
                     'Unknown';
      
      return NotificationStatus(
        isAvailable: false,
        daemon: 'None detected',
        message: 'No notification daemon found. Desktop: $desktop',
        recommendations: _getRecommendations(desktop),
      );
      
    } catch (e) {
      return NotificationStatus(
        isAvailable: false,
        daemon: 'Error checking',
        message: 'Could not check notification daemon status: $e',
        recommendations: _getGenericRecommendations(),
      );
    }
  }
  
  /// Get installation recommendations based on desktop environment
  static List<String> _getRecommendations(String desktop) {
    final lowerDesktop = desktop.toLowerCase();
    
    if (lowerDesktop.contains('gnome')) {
      return [
        'GNOME should have built-in notifications',
        'Try: sudo apt install notification-daemon',
        'Or restart your GNOME session',
      ];
    } else if (lowerDesktop.contains('kde') || lowerDesktop.contains('plasma')) {
      return [
        'KDE should have built-in notifications',
        'Check System Settings > Notifications',
        'Try: sudo apt install plasma-workspace',
      ];
    } else if (lowerDesktop.contains('xfce')) {
      return [
        'Install XFCE notification daemon:',
        'sudo apt install xfce4-notifyd',
        'Or try: sudo apt install notification-daemon',
      ];
    } else if (lowerDesktop.contains('i3') || lowerDesktop.contains('sway')) {
      return [
        'For i3/Sway, install a notification daemon:',
        'sudo apt install dunst  # For X11',
        'sudo apt install mako-notifier  # For Wayland',
        'Add to your config to start automatically',
      ];
    } else {
      return _getGenericRecommendations();
    }
  }
  
  /// Get generic installation recommendations
  static List<String> _getGenericRecommendations() {
    return [
      'Install a notification daemon:',
      'sudo apt install dunst  # Lightweight, works everywhere',
      'sudo apt install notification-daemon  # Standard daemon',
      'sudo apt install notify-osd  # Ubuntu-style notifications',
      '',
      'For Wayland users:',
      'sudo apt install mako-notifier',
      '',
      'After installation, restart your session or run:',
      'dunst &  # or your chosen daemon',
    ];
  }
  
  /// Test notification functionality
  static Future<bool> testNotification() async {
    if (!Platform.isLinux) return true;
    
    try {
      // Try using notify-send command
      final result = await Process.run('notify-send', [
        'Daily Motivator',
        'Test notification - if you see this, notifications are working!',
        '--app-name=Daily Motivator',
        '--urgency=normal',
      ]);
      
      return result.exitCode == 0;
    } catch (e) {
      try {
        // Fallback: try without extra parameters
        final result = await Process.run('notify-send', [
          'Daily Motivator Test',
          'Notifications are working!',
        ]);
        return result.exitCode == 0;
      } catch (e2) {
        return false;
      }
    }
  }
  
  /// Show notification setup dialog
  static void showNotificationSetupDialog(BuildContext context, NotificationStatus status) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              status.isAvailable ? Icons.check_circle : Icons.warning,
              color: status.isAvailable ? Colors.green : Colors.orange,
            ),
            const SizedBox(width: 8),
            const Text('Notification Status'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Daemon: ${status.daemon}'),
              const SizedBox(height: 8),
              Text(status.message),
              if (status.recommendations.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  'Recommendations:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ...status.recommendations.map((rec) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    rec,
                    style: TextStyle(
                      fontFamily: rec.startsWith('sudo') ? 'monospace' : null,
                      fontSize: rec.startsWith('sudo') ? 12 : null,
                    ),
                  ),
                )),
              ],
            ],
          ),
        ),
        actions: [
          if (Platform.isLinux)
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                final success = await testNotification();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        success 
                          ? 'Test notification sent successfully!' 
                          : 'Test notification failed. Check your notification daemon.',
                      ),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                }
              },
              child: const Text('Test Notification'),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

/// Notification status information
class NotificationStatus {
  final bool isAvailable;
  final String daemon;
  final String message;
  final List<String> recommendations;
  
  NotificationStatus({
    required this.isAvailable,
    required this.daemon,
    required this.message,
    this.recommendations = const [],
  });
}
