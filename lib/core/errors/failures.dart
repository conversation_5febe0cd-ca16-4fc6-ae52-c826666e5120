import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;
  final int? code;

  const Failure({required this.message, this.code});

  @override
  List<Object?> get props => [message, code];
}

// General failures
class ServerFailure extends Failure {
  const ServerFailure({required String message, int? code})
      : super(message: message, code: code);
}

class CacheFailure extends Failure {
  const CacheFailure({required String message, int? code})
      : super(message: message, code: code);
}

class NetworkFailure extends Failure {
  const NetworkFailure({required String message, int? code})
      : super(message: message, code: code);
}

class ValidationFailure extends Failure {
  const ValidationFailure({required String message, int? code})
      : super(message: message, code: code);
}

class NotificationFailure extends Failure {
  const NotificationFailure({required String message, int? code})
      : super(message: message, code: code);
}

class PreferencesFailure extends Failure {
  const PreferencesFailure({required String message, int? code})
      : super(message: message, code: code);
}

class UnknownFailure extends Failure {
  const UnknownFailure({required String message, int? code})
      : super(message: message, code: code);
}
