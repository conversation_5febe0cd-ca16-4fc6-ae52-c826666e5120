class ServerException implements Exception {
  final String message;
  final int? code;

  ServerException({required this.message, this.code});

  @override
  String toString() => 'ServerException: $message (code: $code)';
}

class CacheException implements Exception {
  final String message;
  final int? code;

  CacheException({required this.message, this.code});

  @override
  String toString() => 'CacheException: $message (code: $code)';
}

class NetworkException implements Exception {
  final String message;
  final int? code;

  NetworkException({required this.message, this.code});

  @override
  String toString() => 'NetworkException: $message (code: $code)';
}

class ValidationException implements Exception {
  final String message;
  final int? code;

  ValidationException({required this.message, this.code});

  @override
  String toString() => 'ValidationException: $message (code: $code)';
}

class NotificationException implements Exception {
  final String message;
  final int? code;

  NotificationException({required this.message, this.code});

  @override
  String toString() => 'NotificationException: $message (code: $code)';
}

class PreferencesException implements Exception {
  final String message;
  final int? code;

  PreferencesException({required this.message, this.code});

  @override
  String toString() => 'PreferencesException: $message (code: $code)';
}
