import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/platform_utils.dart';

/// Production-level storage service with error handling and data validation
class StorageService {
  static const String _keyPrefix = 'daily_motivator_';
  static const String _userDataKey = '${_keyPrefix}user_data';
  static const String _analyticsKey = '${_keyPrefix}analytics';
  static const String _preferencesKey = '${_keyPrefix}preferences';
  static const String _gamificationKey = '${_keyPrefix}gamification';
  static const String _backupKey = '${_keyPrefix}backup';
  
  static SharedPreferences? _prefs;
  
  /// Initialize storage service
  static Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
    } catch (e) {
      throw Exception('Failed to initialize storage: ${e.toString()}');
    }
  }
  
  /// Get SharedPreferences instance
  static SharedPreferences get _preferences {
    if (_prefs == null) {
      throw Exception('Storage not initialized. Call StorageService.initialize() first.');
    }
    return _prefs!;
  }
  
  /// Save data with error handling and validation
  static Future<bool> saveData<T>(String key, T data) async {
    try {
      final fullKey = _keyPrefix + key;
      
      if (data == null) {
        return await _preferences.remove(fullKey);
      }
      
      if (data is String) {
        return await _preferences.setString(fullKey, data);
      } else if (data is int) {
        return await _preferences.setInt(fullKey, data);
      } else if (data is double) {
        return await _preferences.setDouble(fullKey, data);
      } else if (data is bool) {
        return await _preferences.setBool(fullKey, data);
      } else if (data is List<String>) {
        return await _preferences.setStringList(fullKey, data);
      } else {
        // Serialize complex objects to JSON
        final jsonString = jsonEncode(data);
        return await _preferences.setString(fullKey, jsonString);
      }
    } catch (e) {
      debugPrint('Storage error saving $key: $e');
      throw Exception('Failed to save data for key $key: ${e.toString()}');
    }
  }
  
  /// Load data with type safety and error handling
  static T? loadData<T>(String key, {T? defaultValue}) {
    try {
      final fullKey = _keyPrefix + key;

      if (!_preferences.containsKey(fullKey)) {
        return defaultValue;
      }

      final value = _preferences.get(fullKey);

      if (value == null) {
        return defaultValue;
      }

      // Handle different data types
      if (value is String) {
        // For String type, return directly
        if (T == String) {
          return value as T;
        }
        // For complex objects, try JSON deserialization
        try {
          final decoded = jsonDecode(value);
          return decoded as T;
        } catch (e) {
          debugPrint('Failed to deserialize JSON for key $key: $e');
          return defaultValue;
        }
      } else {
        // For primitive types, try direct casting
        try {
          return value as T;
        } catch (e) {
          debugPrint('Failed to cast value for key $key: $e');
          return defaultValue;
        }
      }
    } catch (e) {
      debugPrint('Storage error loading $key: $e');
      return defaultValue;
    }
  }
  
  /// Save user data
  static Future<bool> saveUserData(Map<String, dynamic> userData) async {
    return await saveData(_userDataKey, userData);
  }
  
  /// Load user data
  static Map<String, dynamic> loadUserData() {
    return loadData<Map<String, dynamic>>(_userDataKey, defaultValue: {}) ?? {};
  }
  
  /// Save analytics data
  static Future<bool> saveAnalyticsData(Map<String, dynamic> analyticsData) async {
    return await saveData(_analyticsKey, analyticsData);
  }
  
  /// Load analytics data
  static Map<String, dynamic> loadAnalyticsData() {
    return loadData<Map<String, dynamic>>(_analyticsKey, defaultValue: {}) ?? {};
  }
  
  /// Save preferences data
  static Future<bool> savePreferencesData(Map<String, dynamic> preferencesData) async {
    return await saveData(_preferencesKey, preferencesData);
  }
  
  /// Load preferences data
  static Map<String, dynamic> loadPreferencesData() {
    return loadData<Map<String, dynamic>>(_preferencesKey, defaultValue: {}) ?? {};
  }
  
  /// Save gamification data
  static Future<bool> saveGamificationData(Map<String, dynamic> gamificationData) async {
    return await saveData(_gamificationKey, gamificationData);
  }
  
  /// Load gamification data
  static Map<String, dynamic> loadGamificationData() {
    return loadData<Map<String, dynamic>>(_gamificationKey, defaultValue: {}) ?? {};
  }
  
  /// Create backup of all data
  static Future<Map<String, dynamic>> createBackup() async {
    try {
      final backup = {
        'version': '1.0.0',
        'timestamp': DateTime.now().toIso8601String(),
        'platform': PlatformUtils.platformName,
        'userData': loadUserData(),
        'analytics': loadAnalyticsData(),
        'preferences': loadPreferencesData(),
        'gamification': loadGamificationData(),
      };
      
      // Save backup locally
      await saveData(_backupKey, backup);
      
      return backup;
    } catch (e) {
      throw Exception('Failed to create backup: ${e.toString()}');
    }
  }
  
  /// Restore from backup
  static Future<bool> restoreFromBackup(Map<String, dynamic> backup) async {
    try {
      // Validate backup format
      if (!backup.containsKey('version') || !backup.containsKey('userData')) {
        throw Exception('Invalid backup format');
      }
      
      // Restore each data type
      if (backup.containsKey('userData')) {
        await saveUserData(backup['userData'] as Map<String, dynamic>);
      }
      
      if (backup.containsKey('analytics')) {
        await saveAnalyticsData(backup['analytics'] as Map<String, dynamic>);
      }
      
      if (backup.containsKey('preferences')) {
        await savePreferencesData(backup['preferences'] as Map<String, dynamic>);
      }
      
      if (backup.containsKey('gamification')) {
        await saveGamificationData(backup['gamification'] as Map<String, dynamic>);
      }
      
      return true;
    } catch (e) {
      throw Exception('Failed to restore from backup: ${e.toString()}');
    }
  }
  
  /// Clear all data (for reset functionality)
  static Future<bool> clearAllData() async {
    try {
      final keys = _preferences.getKeys()
          .where((key) => key.startsWith(_keyPrefix))
          .toList();
      
      for (final key in keys) {
        await _preferences.remove(key);
      }
      
      return true;
    } catch (e) {
      throw Exception('Failed to clear data: ${e.toString()}');
    }
  }
  
  /// Get storage usage statistics
  static Map<String, dynamic> getStorageStats() {
    try {
      final keys = _preferences.getKeys()
          .where((key) => key.startsWith(_keyPrefix))
          .toList();
      
      int totalSize = 0;
      final keyStats = <String, int>{};
      
      for (final key in keys) {
        final value = _preferences.get(key);
        final size = value.toString().length;
        totalSize += size;
        keyStats[key.replaceFirst(_keyPrefix, '')] = size;
      }
      
      return {
        'totalKeys': keys.length,
        'totalSize': totalSize,
        'keyStats': keyStats,
        'platform': PlatformUtils.platformName,
      };
    } catch (e) {
      return {
        'error': e.toString(),
      };
    }
  }
  
  /// Check if storage is healthy
  static Future<bool> healthCheck() async {
    try {
      // Test write
      const testKey = 'health_check_test';
      const testValue = 'test_value';
      
      await saveData(testKey, testValue);
      final readValue = loadData<String>(testKey);
      
      // Clean up test data
      await _preferences.remove(_keyPrefix + testKey);
      
      return readValue == testValue;
    } catch (e) {
      debugPrint('Storage health check failed: $e');
      return false;
    }
  }
  
  /// Export data as JSON string (for sharing/backup)
  static Future<String> exportData() async {
    try {
      final backup = await createBackup();
      return jsonEncode(backup);
    } catch (e) {
      throw Exception('Failed to export data: ${e.toString()}');
    }
  }
  
  /// Import data from JSON string
  static Future<bool> importData(String jsonData) async {
    try {
      final data = jsonDecode(jsonData) as Map<String, dynamic>;
      return await restoreFromBackup(data);
    } catch (e) {
      throw Exception('Failed to import data: ${e.toString()}');
    }
  }
}
