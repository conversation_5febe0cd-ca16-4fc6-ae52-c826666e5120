import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:permission_handler/permission_handler.dart';
import '../errors/exceptions.dart';
import '../../domain/entities/quote.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      const DarwinInitializationSettings initializationSettingsIOS = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // Linux desktop notification settings
      final LinuxInitializationSettings initializationSettingsLinux = LinuxInitializationSettings(
        defaultActionName: 'Open notification',
        defaultIcon: AssetsLinuxIcon('assets/images/app_icon.png'),
      );

      final InitializationSettings initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
        linux: initializationSettingsLinux,
      );

      final bool? initialized = await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      if (initialized != true) {
        throw NotificationException(
          message: 'Failed to initialize notification plugin',
        );
      }

      _isInitialized = true;
    } catch (e) {
      throw NotificationException(
        message: 'Failed to initialize notifications: ${e.toString()}',
      );
    }
  }

  Future<bool> requestPermissions() async {
    try {
      if (Platform.isAndroid) {
        final status = await Permission.notification.request();
        return status.isGranted;
      } else if (Platform.isIOS) {
        final result = await _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
            ?.requestPermissions(
              alert: true,
              badge: true,
              sound: true,
            );
        return result ?? false;
      } else if (Platform.isLinux) {
        // Linux desktop notifications don't require explicit permission requests
        // They are handled by the desktop environment (GNOME, KDE, etc.)
        return true;
      } else if (Platform.isWindows || Platform.isMacOS) {
        // Desktop platforms generally don't require permission requests
        return true;
      }
      return true;
    } catch (e) {
      // On Linux, permission errors are often due to missing notification daemon
      // Return true to allow the app to continue functioning
      if (Platform.isLinux) {
        debugPrint('Linux notification permission warning: ${e.toString()}');
        return true;
      }
      throw NotificationException(
        message: 'Failed to request notification permissions: ${e.toString()}',
      );
    }
  }

  Future<void> scheduleQuoteNotifications({
    required List<TimeOfDay> times,
    required List<String> categories,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      // Verify timezone is initialized
      try {
        tz.local;
      } catch (e) {
        throw NotificationException(
          message: 'Timezone not initialized. Please restart the app.',
        );
      }

      // Cancel existing notifications
      await cancelAllNotifications();

      // Schedule new notifications
      for (int i = 0; i < times.length; i++) {
        await _scheduleRepeatingNotification(
          id: i,
          time: times[i],
          title: 'Daily Motivation 💪',
          body: 'Time for your daily dose of inspiration!',
        );
      }
    } catch (e) {
      throw NotificationException(
        message: 'Failed to schedule notifications: ${e.toString()}',
      );
    }
  }

  Future<void> showQuoteNotification({
    required Quote quote,
    int id = 0,
  }) async {
    try {
      if (!_isInitialized) await initialize();

      const AndroidNotificationDetails androidPlatformChannelSpecifics = AndroidNotificationDetails(
        'quote_channel',
        'Daily Quotes',
        channelDescription: 'Notifications for daily motivational quotes',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: false,
        icon: '@mipmap/ic_launcher',
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await _flutterLocalNotificationsPlugin.show(
        id,
        'Daily Motivation 💪',
        _truncateText(quote.text, 100),
        platformChannelSpecifics,
        payload: quote.id,
      );
    } catch (e) {
      throw NotificationException(
        message: 'Failed to show notification: ${e.toString()}',
      );
    }
  }

  Future<void> _scheduleRepeatingNotification({
    required int id,
    required TimeOfDay time,
    required String title,
    required String body,
  }) async {
    try {
      final now = DateTime.now();
      var scheduledDate = DateTime(
        now.year,
        now.month,
        now.day,
        time.hour,
        time.minute,
      );

      // If the scheduled time has already passed today, schedule for tomorrow
      if (scheduledDate.isBefore(now)) {
        scheduledDate = scheduledDate.add(const Duration(days: 1));
      }

      const AndroidNotificationDetails androidPlatformChannelSpecifics = AndroidNotificationDetails(
        'quote_channel',
        'Daily Quotes',
        channelDescription: 'Notifications for daily motivational quotes',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: false,
        icon: '@mipmap/ic_launcher',
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const LinuxNotificationDetails linuxPlatformChannelSpecifics = LinuxNotificationDetails(
        actions: [
          LinuxNotificationAction(
            key: 'open_app',
            label: 'Open App',
          ),
        ],
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
        linux: linuxPlatformChannelSpecifics,
      );

      await _flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        tz.TZDateTime.from(scheduledDate, tz.local),
        platformChannelSpecifics,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents.time,
      );
    } catch (e) {
      // On Linux, notification scheduling might fail if no notification daemon is running
      // Log the error but don't crash the app
      if (Platform.isLinux) {
        debugPrint('Linux notification scheduling failed: ${e.toString()}');
        debugPrint('Make sure a notification daemon (like dunst, mako, or desktop environment notifications) is running');
        return; // Silently fail on Linux to keep app functional
      }
      throw NotificationException(
        message: 'Failed to schedule notification: ${e.toString()}',
      );
    }
  }

  Future<void> cancelAllNotifications() async {
    try {
      await _flutterLocalNotificationsPlugin.cancelAll();
    } catch (e) {
      if (Platform.isLinux) {
        debugPrint('Linux notification cancellation failed: ${e.toString()}');
        return; // Silently fail on Linux
      }
      throw NotificationException(
        message: 'Failed to cancel notifications: ${e.toString()}',
      );
    }
  }

  Future<void> cancelNotification(int id) async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(id);
    } catch (e) {
      if (Platform.isLinux) {
        debugPrint('Linux notification cancellation failed: ${e.toString()}');
        return; // Silently fail on Linux
      }
      throw NotificationException(
        message: 'Failed to cancel notification: ${e.toString()}',
      );
    }
  }

  void _onNotificationTapped(NotificationResponse notificationResponse) {
    // Handle notification tap
    // You can navigate to a specific screen or perform an action
    debugPrint('Notification tapped: ${notificationResponse.payload}');
  }

  String _truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength - 3)}...';
  }

  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
  }
}
