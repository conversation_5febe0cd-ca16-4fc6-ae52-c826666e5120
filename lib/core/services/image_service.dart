import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:math' as math;
import '../utils/image_generator.dart';

class ImageService {
  static const String _baseAssetPath = 'assets/images';
  
  // Category images mapping
  static const Map<String, String> _categoryImages = {
    'motivation': '$_baseAssetPath/categories/motivation.jpg',
    'success': '$_baseAssetPath/categories/success.jpg',
    'life': '$_baseAssetPath/categories/life.jpg',
    'wisdom': '$_baseAssetPath/categories/wisdom.jpg',
    'happiness': '$_baseAssetPath/categories/happiness.jpg',
    'inspiration': '$_baseAssetPath/categories/inspiration.jpg',
    'leadership': '$_baseAssetPath/categories/leadership.jpg',
    'growth': '$_baseAssetPath/categories/growth.jpg',
    'mindfulness': '$_baseAssetPath/categories/mindfulness.jpg',
    'courage': '$_baseAssetPath/categories/courage.jpg',
  };

  // Background images
  static const List<String> _backgroundImages = [
    '$_baseAssetPath/backgrounds/gradient1.jpg',
    '$_baseAssetPath/backgrounds/gradient2.jpg',
    '$_baseAssetPath/backgrounds/gradient3.jpg',
    '$_baseAssetPath/backgrounds/nature1.jpg',
    '$_baseAssetPath/backgrounds/nature2.jpg',
    '$_baseAssetPath/backgrounds/abstract1.jpg',
    '$_baseAssetPath/backgrounds/abstract2.jpg',
  ];

  // Achievement images
  static const Map<String, String> _achievementImages = {
    'first_quote': '$_baseAssetPath/achievements/first_star.png',
    'streak_3': '$_baseAssetPath/achievements/fire_3.png',
    'streak_7': '$_baseAssetPath/achievements/fire_7.png',
    'streak_30': '$_baseAssetPath/achievements/crown.png',
    'category_explorer': '$_baseAssetPath/achievements/compass.png',
    'quote_master': '$_baseAssetPath/achievements/book.png',
    'early_bird': '$_baseAssetPath/achievements/sunrise.png',
    'night_owl': '$_baseAssetPath/achievements/moon.png',
    'weekend_warrior': '$_baseAssetPath/achievements/weekend.png',
    'perfect_week': '$_baseAssetPath/achievements/perfect.png',
  };

  // Avatar images
  static const List<String> _avatarImages = [
    '$_baseAssetPath/avatars/avatar1.png',
    '$_baseAssetPath/avatars/avatar2.png',
    '$_baseAssetPath/avatars/avatar3.png',
    '$_baseAssetPath/avatars/avatar4.png',
    '$_baseAssetPath/avatars/avatar5.png',
  ];

  // Get category image
  static String? getCategoryImage(String category) {
    return _categoryImages[category.toLowerCase()];
  }

  // Get random background image
  static String getRandomBackgroundImage() {
    final random = math.Random();
    return _backgroundImages[random.nextInt(_backgroundImages.length)];
  }

  // Get background image by index
  static String getBackgroundImage(int index) {
    return _backgroundImages[index % _backgroundImages.length];
  }

  // Get achievement image
  static String? getAchievementImage(String achievementKey) {
    return _achievementImages[achievementKey];
  }

  // Get random avatar
  static String getRandomAvatar() {
    final random = math.Random();
    return _avatarImages[random.nextInt(_avatarImages.length)];
  }

  // Get avatar by index
  static String getAvatar(int index) {
    return _avatarImages[index % _avatarImages.length];
  }

  // Create image widget with fallback
  static Widget buildImage({
    required String? imagePath,
    required Widget fallback,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    Color? color,
    BlendMode? colorBlendMode,
  }) {
    if (imagePath == null) {
      return fallback;
    }

    Widget imageWidget;

    if (imagePath.startsWith('http')) {
      // Network image
      imageWidget = CachedNetworkImage(
        imageUrl: imagePath,
        width: width,
        height: height,
        fit: fit,
        color: color,
        colorBlendMode: colorBlendMode,
        placeholder: (context, url) => _buildPlaceholder(width, height),
        errorWidget: (context, url, error) => fallback,
      );
    } else {
      // Asset image
      imageWidget = Image.asset(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        color: color,
        colorBlendMode: colorBlendMode,
        errorBuilder: (context, error, stackTrace) => fallback,
      );
    }

    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  // Build placeholder widget
  static Widget _buildPlaceholder(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.grey.shade300,
            Colors.grey.shade100,
            Colors.grey.shade300,
          ],
          stops: const [0.0, 0.5, 1.0],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: const Center(
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  // Create category icon with image background
  static Widget buildCategoryIcon({
    required String category,
    required double size,
    required Color fallbackColor,
    required IconData fallbackIcon,
  }) {
    final imagePath = getCategoryImage(category);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(size / 4),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(size / 4),
        child: buildImage(
          imagePath: imagePath,
          width: size,
          height: size,
          borderRadius: BorderRadius.circular(size / 4),
          fallback: ImageGenerator.generateCategoryImage(
            category: category,
            width: size,
            height: size,
            borderRadius: BorderRadius.circular(size / 4),
          ),
        ),
      ),
    );
  }

  // Create achievement badge with image
  static Widget buildAchievementBadge({
    required String achievementKey,
    required double size,
    required Color fallbackColor,
    required IconData fallbackIcon,
    bool isUnlocked = true,
  }) {
    final imagePath = getAchievementImage(achievementKey);

    return buildImage(
      imagePath: imagePath,
      width: size,
      height: size,
      color: isUnlocked ? null : Colors.grey,
      colorBlendMode: isUnlocked ? null : BlendMode.saturation,
      fallback: ImageGenerator.generateAchievementBadge(
        achievementKey: achievementKey,
        size: size,
        isUnlocked: isUnlocked,
      ),
    );
  }

  // Create avatar widget
  static Widget buildAvatar({
    required int userLevel,
    required double size,
    String? customImagePath,
  }) {
    final imagePath = customImagePath ?? getAvatar(userLevel);

    return buildImage(
      imagePath: imagePath,
      width: size,
      height: size,
      fallback: ImageGenerator.generateAvatar(
        userLevel: userLevel,
        size: size,
      ),
    );
  }

  // Create background image widget for quotes
  static Widget buildQuoteBackground({
    required Widget child,
    String? backgroundImage,
    List<Color>? gradientColors,
    int? backgroundIndex,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            // Background image or gradient
            Positioned.fill(
              child: backgroundImage != null
                  ? buildImage(
                      imagePath: backgroundImage,
                      fallback: _buildGradientBackground(gradientColors, backgroundIndex),
                    )
                  : _buildGradientBackground(gradientColors, backgroundIndex),
            ),

            // Overlay for better text readability
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.2),
                      Colors.black.withOpacity(0.4),
                    ],
                  ),
                ),
              ),
            ),

            // Content
            child,
          ],
        ),
      ),
    );
  }

  // Build gradient background with generated patterns
  static Widget _buildGradientBackground(List<Color>? gradientColors, int? backgroundIndex) {
    if (backgroundIndex != null) {
      return ImageGenerator.generateBackgroundImage(
        index: backgroundIndex,
        width: double.infinity,
        height: double.infinity,
      );
    }

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: gradientColors ?? [
            Colors.blue.shade400,
            Colors.purple.shade400,
          ],
        ),
      ),
    );
  }

  // Preload critical images
  static Future<void> preloadCriticalImages(BuildContext context) async {
    final futures = <Future>[];
    
    // Preload category images
    for (final imagePath in _categoryImages.values) {
      futures.add(
        precacheImage(AssetImage(imagePath), context)
            .catchError((_) => null),
      );
    }
    
    // Preload some background images
    for (int i = 0; i < 3; i++) {
      futures.add(
        precacheImage(AssetImage(_backgroundImages[i]), context)
            .catchError((_) => null),
      );
    }
    
    // Preload achievement images
    for (final imagePath in _achievementImages.values) {
      futures.add(
        precacheImage(AssetImage(imagePath), context)
            .catchError((_) => null),
      );
    }
    
    await Future.wait(futures);
  }
}
