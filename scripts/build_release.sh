#!/bin/bash

echo "🚀 Building Daily Motivator for Release"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

print_status "Flutter found: $(flutter --version | head -n 1)"

# Clean previous builds
print_status "Cleaning previous builds..."
flutter clean
flutter pub get

# Generate app icons
print_status "Generating app icons..."
if flutter pub run flutter_launcher_icons:main; then
    print_status "App icons generated successfully"
else
    print_warning "Failed to generate app icons - continuing anyway"
fi

# Run tests
print_status "Running tests..."
if flutter test; then
    print_status "All tests passed"
else
    print_error "Tests failed - aborting build"
    exit 1
fi

# Build for Android
echo ""
echo "📱 Building Android Release..."
echo "=============================="

if flutter build appbundle --release; then
    print_status "Android App Bundle built successfully"
    print_status "Location: build/app/outputs/bundle/release/app-release.aab"
else
    print_error "Android build failed"
    exit 1
fi

# Build APK as well
if flutter build apk --release; then
    print_status "Android APK built successfully"
    print_status "Location: build/app/outputs/flutter-apk/app-release.apk"
else
    print_warning "APK build failed - AAB is sufficient for Play Store"
fi

# Build for iOS (only on macOS)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo ""
    echo "🍎 Building iOS Release..."
    echo "========================="
    
    if flutter build ios --release; then
        print_status "iOS build completed successfully"
        print_status "Open ios/Runner.xcworkspace in Xcode to archive and upload"
    else
        print_error "iOS build failed"
        exit 1
    fi
else
    print_warning "iOS build skipped (not running on macOS)"
fi

# Build for Web
echo ""
echo "🌐 Building Web Release..."
echo "========================="

if flutter build web --release; then
    print_status "Web build completed successfully"
    print_status "Location: build/web/"
else
    print_warning "Web build failed"
fi

# Summary
echo ""
echo "📋 Build Summary"
echo "================"
print_status "Android App Bundle: build/app/outputs/bundle/release/app-release.aab"
if [[ -f "build/app/outputs/flutter-apk/app-release.apk" ]]; then
    print_status "Android APK: build/app/outputs/flutter-apk/app-release.apk"
fi
if [[ "$OSTYPE" == "darwin"* ]]; then
    print_status "iOS: Ready for Xcode archiving"
fi
if [[ -d "build/web" ]]; then
    print_status "Web: build/web/"
fi

echo ""
print_status "🎉 Build process completed!"
echo ""
echo "Next steps:"
echo "1. For Android: Upload app-release.aab to Google Play Console"
echo "2. For iOS: Open Xcode, archive, and upload to App Store Connect"
echo "3. For Web: Deploy build/web/ to your hosting provider"
