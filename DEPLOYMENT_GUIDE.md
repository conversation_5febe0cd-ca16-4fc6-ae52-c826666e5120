# Daily Motivator - Deployment Guide

This guide covers deploying the Daily Motivator app to Google Play Store and Apple App Store.

## Prerequisites

- Flutter SDK (latest stable version)
- Android Studio with Android SDK
- Xcode (for iOS deployment, macOS only)
- Google Play Console account ($25 one-time fee)
- Apple Developer account ($99/year)

## Pre-Deployment Checklist

### 1. App Configuration

Update `pubspec.yaml` with production values:
```yaml
name: daily_motivator
description: Daily motivational quotes with customizable notifications
version: 1.0.0+1
```

### 2. Generate App Icons
```bash
# Convert SVG to PNG (512x512)
# Use online converter or ImageMagick
flutter pub run flutter_launcher_icons:main
```

### 3. Update App Identifiers

**Android** (`android/app/build.gradle`):
```gradle
defaultConfig {
    applicationId "com.yourcompany.dailymotivator"
    minSdk 21
    targetSdk 34
    versionCode 1
    versionName "1.0.0"
}
```

**iOS** (`ios/Runner/Info.plist`):
```xml
<key>CFBundleIdentifier</key>
<string>com.yourcompany.dailymotivator</string>
```

### 4. Test the App
```bash
# Run tests
flutter test

# Test on devices
flutter run --release
```

## Android Deployment (Google Play Store)

### Step 1: Create Signing Key

```bash
# Generate keystore
keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload

# Create key.properties file
echo "storePassword=YOUR_STORE_PASSWORD
keyPassword=YOUR_KEY_PASSWORD
keyAlias=upload
storeFile=/path/to/upload-keystore.jks" > android/key.properties
```

### Step 2: Configure Signing

Update `android/app/build.gradle`:
```gradle
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
    }
}
```

### Step 3: Build Release APK/AAB

```bash
# Build App Bundle (recommended)
flutter build appbundle --release

# Or build APK
flutter build apk --release
```

### Step 4: Google Play Console Setup

1. **Create App**:
   - Go to [Google Play Console](https://play.google.com/console)
   - Create new app
   - Fill in app details

2. **App Content**:
   - Privacy Policy (required)
   - App category: Lifestyle
   - Content rating: Everyone
   - Target audience: 13+

3. **Store Listing**:
   - App name: "Daily Motivator"
   - Short description: "Daily motivational quotes with notifications"
   - Full description: [See template below]
   - Screenshots: 2-8 screenshots per device type
   - Feature graphic: 1024x500px

4. **Release**:
   - Upload AAB file
   - Add release notes
   - Review and publish

### Store Listing Template

**Short Description:**
```
Get daily motivation with personalized inspirational quotes and customizable notifications.
```

**Full Description:**
```
🌟 Daily Motivator - Your Personal Inspiration Companion

Start each day with purpose and positivity! Daily Motivator delivers carefully curated motivational quotes directly to your device, helping you stay inspired and focused on your goals.

✨ KEY FEATURES:
• Personalized quote categories (Success, Happiness, Wisdom, and more)
• Customizable notification frequency (1-5 quotes per day)
• Smart scheduling - set specific times or let us choose optimal moments
• Beautiful Material Design interface
• Offline-ready with real-time quote fetching
• Share your favorite quotes with friends

🎯 PERFECT FOR:
• Anyone seeking daily motivation and inspiration
• Goal-oriented individuals
• People looking to develop positive thinking habits
• Those who appreciate wisdom and life quotes

📱 FEATURES:
• Clean, intuitive interface
• Multiple quote categories to match your interests
• Flexible notification settings
• Quote sharing capabilities
• Responsive design for all screen sizes

Transform your daily routine with meaningful inspiration. Download Daily Motivator today and take the first step toward a more motivated you!

🔔 Note: Notification permissions are required for the best experience.
```

## iOS Deployment (Apple App Store)

### Step 1: Xcode Configuration

1. Open `ios/Runner.xcworkspace` in Xcode
2. Update Bundle Identifier
3. Set Team (requires Apple Developer account)
4. Configure App Icons and Launch Screen

### Step 2: App Store Connect Setup

1. **Create App**:
   - Go to [App Store Connect](https://appstoreconnect.apple.com)
   - Create new app
   - Fill in app information

2. **App Information**:
   - Name: "Daily Motivator"
   - Bundle ID: com.yourcompany.dailymotivator
   - SKU: dailymotivator
   - Primary Language: English

3. **Pricing and Availability**:
   - Price: Free
   - Availability: All countries

### Step 3: Build and Archive

```bash
# Build iOS release
flutter build ios --release

# Or use Xcode:
# 1. Open ios/Runner.xcworkspace
# 2. Select "Any iOS Device"
# 3. Product > Archive
# 4. Upload to App Store Connect
```

### Step 4: App Store Listing

**App Store Information:**
- **Subtitle**: "Daily Motivational Quotes"
- **Keywords**: motivation,quotes,inspiration,daily,positive,mindset,goals,success,happiness,wisdom
- **Description**: [Use same as Android with iOS-specific formatting]
- **What's New**: "Initial release with personalized motivational quotes and smart notifications"

**App Review Information:**
- **Notes**: "App requires notification permissions for core functionality. Uses ZenQuotes.io API for quote content."
- **Contact Information**: Your support email
- **Demo Account**: Not required

**Screenshots Required:**
- iPhone 6.7": 1290x2796 (3 screenshots minimum)
- iPhone 6.5": 1242x2688 (3 screenshots minimum)
- iPad Pro 12.9": 2048x2732 (3 screenshots minimum)

### Step 5: Submit for Review

1. Complete all required fields
2. Add screenshots and app preview (optional)
3. Set release options (manual/automatic)
4. Submit for review

## Post-Deployment

### Analytics Setup

Add Firebase Analytics (optional):
```bash
flutter pub add firebase_analytics
flutter pub add firebase_core
```

### Monitoring

1. **Crash Reporting**: Firebase Crashlytics
2. **Performance**: Firebase Performance
3. **User Feedback**: In-app feedback system

### Updates

For app updates:
1. Increment version in `pubspec.yaml`
2. Update version codes in platform files
3. Build and upload new version
4. Add release notes

## Common Issues and Solutions

### Android Issues

**Issue**: "App not signed"
**Solution**: Ensure signing configuration is correct in `build.gradle`

**Issue**: "Notification permissions"
**Solution**: Target SDK 33+ requires runtime permission requests

### iOS Issues

**Issue**: "Invalid Bundle"
**Solution**: Check Bundle ID matches App Store Connect

**Issue**: "Missing compliance"
**Solution**: Declare encryption usage in App Store Connect

## Security Considerations

1. **API Keys**: Store securely, don't commit to version control
2. **Permissions**: Request only necessary permissions
3. **Data Privacy**: Implement privacy policy
4. **Network Security**: Use HTTPS for all API calls

## Support and Maintenance

1. **User Support**: Set up support email
2. **Bug Tracking**: Use GitHub Issues or similar
3. **Regular Updates**: Plan monthly updates
4. **User Feedback**: Monitor store reviews

## Estimated Timeline

- **Android**: 2-3 days for review
- **iOS**: 1-7 days for review
- **Total Setup Time**: 1-2 days for experienced developers

## Cost Breakdown

- **Google Play**: $25 one-time registration
- **Apple App Store**: $99/year developer program
- **Optional**: Firebase (free tier available)

---

**Need Help?**
- Flutter Documentation: https://docs.flutter.dev/deployment
- Google Play Console Help: https://support.google.com/googleplay/android-developer
- App Store Connect Help: https://developer.apple.com/help/app-store-connect
