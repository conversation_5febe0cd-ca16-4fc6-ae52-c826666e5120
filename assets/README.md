# Daily Motivator Assets

## App Icons

To generate app icons from the SVG file:

1. Convert the SVG to PNG (512x512):
   ```bash
   # Using ImageMagick (if installed)
   convert -background none -size 512x512 assets/images/app_icon.svg assets/images/app_icon.png
   
   # Or use an online converter like:
   # - https://convertio.co/svg-png/
   # - https://cloudconvert.com/svg-to-png
   ```

2. Create the adaptive icon foreground (same as main icon but transparent background):
   ```bash
   cp assets/images/app_icon.png assets/images/app_icon_foreground.png
   ```

3. Generate launcher icons:
   ```bash
   flutter pub get
   flutter pub run flutter_launcher_icons:main
   ```

## Icon Requirements

- **Main Icon**: 512x512 PNG with transparent or solid background
- **Adaptive Icon Foreground**: 512x512 PNG with transparent background
- **Background Color**: #FFFFFF (white)
- **Theme Color**: #673AB7 (deep purple)

## Design Elements

The app icon features:
- Deep purple gradient background (#673AB7 to #512DA8)
- White quote marks symbolizing inspirational quotes
- "DAILY MOTIVATOR" text
- Decorative stars for visual appeal
- Clean, modern Material Design aesthetic

## Alternative Icons

If you want to create custom icons:
1. Use the same color scheme (#673AB7, #512DA8, #FFFFFF)
2. Include quote-related imagery (quote marks, speech bubbles, etc.)
3. Keep text readable at small sizes
4. Ensure good contrast for accessibility
