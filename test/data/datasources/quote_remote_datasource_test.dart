import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:http/http.dart' as http;

import 'package:daily_motivator/core/network/api_client.dart';
import 'package:daily_motivator/core/errors/exceptions.dart';
import 'package:daily_motivator/data/datasources/quote_remote_datasource.dart';
import 'package:daily_motivator/data/models/quote_model.dart';

import 'quote_remote_datasource_test.mocks.dart';

@GenerateMocks([ApiClient])
void main() {
  late QuoteRemoteDataSourceImpl dataSource;
  late MockApiClient mockApiClient;

  setUp(() {
    mockApiClient = MockApiClient();
    dataSource = QuoteRemoteDataSourceImpl(apiClient: mockApiClient);
  });

  group('getRandomQuote', () {
    const tQuoteJson = [
      {
        'q': 'The only way to do great work is to love what you do.',
        'a': '<PERSON>',
      }
    ];

    test('should return QuoteModel when the call to API is successful', () async {
      // arrange
      when(mockApiClient.get(any)).thenAnswer((_) async => tQuoteJson);

      // act
      final result = await dataSource.getRandomQuote();

      // assert
      expect(result, isA<QuoteModel>());
      expect(result.text, 'The only way to do great work is to love what you do.');
      expect(result.author, 'Steve Jobs');
      expect(result.category, 'general');
    });

    test('should return fallback quote when API call fails', () async {
      // arrange
      when(mockApiClient.get(any)).thenThrow(ServerException());

      // act
      final result = await dataSource.getRandomQuote();

      // assert
      expect(result, isA<QuoteModel>());
      expect(result.text, 'Believe you can and you\'re halfway there.');
      expect(result.author, 'Theodore Roosevelt');
      expect(result.category, 'motivation');
    });

    test('should call ApiClient with correct URL', () async {
      // arrange
      when(mockApiClient.get(any)).thenAnswer((_) async => tQuoteJson);

      // act
      await dataSource.getRandomQuote();

      // assert
      verify(mockApiClient.get('https://zenquotes.io/api/random'));
    });
  });
}
