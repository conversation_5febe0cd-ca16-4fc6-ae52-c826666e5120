import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:daily_motivator/data/datasources/preferences_local_datasource.dart';
import 'package:daily_motivator/data/repositories/preferences_repository_impl.dart';

import 'preferences_repository_test.mocks.dart';

@GenerateMocks([PreferencesLocalDataSource])
void main() {
  late PreferencesRepositoryImpl repository;
  late MockPreferencesLocalDataSource mockLocalDataSource;

  setUp(() {
    mockLocalDataSource = MockPreferencesLocalDataSource();
    repository = PreferencesRepositoryImpl(localDataSource: mockLocalDataSource);
  });

  group('getSelectedCategories', () {
    test('should return categories from local data source', () async {
      // arrange
      const tCategories = ['success', 'motivation', 'happiness'];
      when(mockLocalDataSource.getSelectedCategories())
          .thenAnswer((_) async => tCategories);

      // act
      final result = await repository.getSelectedCategories();

      // assert
      expect(result, tCategories);
      verify(mockLocalDataSource.getSelectedCategories());
    });
  });

  group('saveSelectedCategories', () {
    test('should call local data source to save categories', () async {
      // arrange
      const tCategories = ['success', 'motivation'];

      // act
      await repository.saveSelectedCategories(tCategories);

      // assert
      verify(mockLocalDataSource.saveSelectedCategories(tCategories));
    });
  });

  group('getNotificationCount', () {
    test('should return notification count from local data source', () async {
      // arrange
      const tCount = 3;
      when(mockLocalDataSource.getNotificationCount())
          .thenAnswer((_) async => tCount);

      // act
      final result = await repository.getNotificationCount();

      // assert
      expect(result, tCount);
      verify(mockLocalDataSource.getNotificationCount());
    });
  });

  group('getNotificationTimes', () {
    test('should return times from local data source when available', () async {
      // arrange
      const tTimes = [
        TimeOfDay(hour: 9, minute: 0),
        TimeOfDay(hour: 15, minute: 30),
      ];
      when(mockLocalDataSource.getNotificationTimes())
          .thenAnswer((_) async => tTimes);

      // act
      final result = await repository.getNotificationTimes();

      // assert
      expect(result, tTimes);
      verify(mockLocalDataSource.getNotificationTimes());
    });

    test('should generate random times when no times are saved', () async {
      // arrange
      when(mockLocalDataSource.getNotificationTimes())
          .thenAnswer((_) async => []);
      when(mockLocalDataSource.getNotificationCount())
          .thenAnswer((_) async => 2);

      // act
      final result = await repository.getNotificationTimes();

      // assert
      expect(result.length, 2);
      expect(result.every((time) => time.hour >= 8 && time.hour <= 20), true);
      verify(mockLocalDataSource.getNotificationTimes());
    });
  });

  group('isOnboardingComplete', () {
    test('should return onboarding status from local data source', () async {
      // arrange
      const tStatus = true;
      when(mockLocalDataSource.isOnboardingComplete())
          .thenAnswer((_) async => tStatus);

      // act
      final result = await repository.isOnboardingComplete();

      // assert
      expect(result, tStatus);
      verify(mockLocalDataSource.isOnboardingComplete());
    });
  });
}
