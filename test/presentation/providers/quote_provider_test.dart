import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:daily_motivator/domain/entities/quote.dart';
import 'package:daily_motivator/domain/usecases/get_random_quote.dart';
import 'package:daily_motivator/presentation/providers/quote_provider.dart';

import 'quote_provider_test.mocks.dart';

@GenerateMocks([GetRandomQuote])
void main() {
  late QuoteProvider provider;
  late MockGetRandomQuote mockGetRandomQuote;

  setUp(() {
    mockGetRandomQuote = MockGetRandomQuote();
    provider = QuoteProvider(getRandomQuote: mockGetRandomQuote);
  });

  group('fetchRandomQuote', () {
    final tQuote = Quote(
      text: 'Test quote',
      author: 'Test Author',
      category: 'motivation',
    );

    test('should get quote from the use case', () async {
      // arrange
      when(mockGetRandomQuote.execute()).thenAnswer((_) async => tQuote);

      // act
      await provider.fetchRandomQuote();

      // assert
      verify(mockGetRandomQuote.execute());
    });

    test('should emit [loading, loaded] when data is gotten successfully', () async {
      // arrange
      when(mockGetRandomQuote.execute()).thenAnswer((_) async => tQuote);

      // assert later
      final expected = [
        QuoteStatus.loading,
        QuoteStatus.loaded,
      ];
      expectLater(provider.status, emitsInOrder(expected));

      // act
      await provider.fetchRandomQuote();
    });

    test('should emit [loading, error] when getting data fails', () async {
      // arrange
      when(mockGetRandomQuote.execute()).thenThrow(Exception('Server error'));

      // assert later
      final expected = [
        QuoteStatus.loading,
        QuoteStatus.error,
      ];
      expectLater(provider.status, emitsInOrder(expected));

      // act
      await provider.fetchRandomQuote();
    });

    test('should cache the quote when data is gotten successfully', () async {
      // arrange
      when(mockGetRandomQuote.execute()).thenAnswer((_) async => tQuote);

      // act
      await provider.fetchRandomQuote();

      // assert
      expect(provider.currentQuote, tQuote);
    });

    test('should set error message when getting data fails', () async {
      // arrange
      const errorMessage = 'Server error';
      when(mockGetRandomQuote.execute()).thenThrow(Exception(errorMessage));

      // act
      await provider.fetchRandomQuote();

      // assert
      expect(provider.errorMessage, contains(errorMessage));
    });
  });
}
