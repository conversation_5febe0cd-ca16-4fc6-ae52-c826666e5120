import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:daily_motivator/core/utils/time_utils.dart';

void main() {
  group('TimeUtils', () {
    group('formatTimeOfDay', () {
      test('should format morning time correctly', () {
        // arrange
        const timeOfDay = TimeOfDay(hour: 9, minute: 30);

        // act
        final result = TimeUtils.formatTimeOfDay(timeOfDay);

        // assert
        expect(result, '9:30 AM');
      });

      test('should format afternoon time correctly', () {
        // arrange
        const timeOfDay = TimeOfDay(hour: 15, minute: 45);

        // act
        final result = TimeUtils.formatTimeOfDay(timeOfDay);

        // assert
        expect(result, '3:45 PM');
      });

      test('should format midnight correctly', () {
        // arrange
        const timeOfDay = TimeOfDay(hour: 0, minute: 0);

        // act
        final result = TimeUtils.formatTimeOfDay(timeOfDay);

        // assert
        expect(result, '12:00 AM');
      });

      test('should format noon correctly', () {
        // arrange
        const timeOfDay = TimeOfDay(hour: 12, minute: 0);

        // act
        final result = TimeUtils.formatTimeOfDay(timeOfDay);

        // assert
        expect(result, '12:00 PM');
      });
    });

    group('parseTimeString', () {
      test('should parse morning time correctly', () {
        // arrange
        const timeString = '9:30 AM';

        // act
        final result = TimeUtils.parseTimeString(timeString);

        // assert
        expect(result.hour, 9);
        expect(result.minute, 30);
      });

      test('should parse afternoon time correctly', () {
        // arrange
        const timeString = '3:45 PM';

        // act
        final result = TimeUtils.parseTimeString(timeString);

        // assert
        expect(result.hour, 15);
        expect(result.minute, 45);
      });
    });

    group('generateRandomTimes', () {
      test('should generate correct number of times', () {
        // act
        final result = TimeUtils.generateRandomTimes(3);

        // assert
        expect(result.length, 3);
      });

      test('should generate times within valid range', () {
        // act
        final result = TimeUtils.generateRandomTimes(5);

        // assert
        for (final time in result) {
          expect(time.hour, greaterThanOrEqualTo(8));
          expect(time.hour, lessThanOrEqualTo(20));
          expect(time.minute, greaterThanOrEqualTo(0));
          expect(time.minute, lessThan(60));
        }
      });

      test('should return times in chronological order', () {
        // act
        final result = TimeUtils.generateRandomTimes(4);

        // assert
        for (int i = 0; i < result.length - 1; i++) {
          final current = result[i];
          final next = result[i + 1];
          
          if (current.hour == next.hour) {
            expect(current.minute, lessThanOrEqualTo(next.minute));
          } else {
            expect(current.hour, lessThan(next.hour));
          }
        }
      });
    });

    group('timeOfDayListToStringList', () {
      test('should convert list of TimeOfDay to list of strings', () {
        // arrange
        const times = [
          TimeOfDay(hour: 9, minute: 0),
          TimeOfDay(hour: 15, minute: 30),
        ];

        // act
        final result = TimeUtils.timeOfDayListToStringList(times);

        // assert
        expect(result.length, 2);
        expect(result[0], '9:00 AM');
        expect(result[1], '3:30 PM');
      });
    });

    group('stringListToTimeOfDayList', () {
      test('should convert list of strings to list of TimeOfDay', () {
        // arrange
        const timeStrings = ['9:00 AM', '3:30 PM'];

        // act
        final result = TimeUtils.stringListToTimeOfDayList(timeStrings);

        // assert
        expect(result.length, 2);
        expect(result[0].hour, 9);
        expect(result[0].minute, 0);
        expect(result[1].hour, 15);
        expect(result[1].minute, 30);
      });
    });
  });
}
