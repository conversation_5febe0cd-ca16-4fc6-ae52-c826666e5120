import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'package:daily_motivator/domain/entities/quote.dart';
import 'package:daily_motivator/domain/repositories/quote_repository.dart';
import 'package:daily_motivator/domain/usecases/get_random_quote.dart';

import 'get_random_quote_test.mocks.dart';

@GenerateMocks([QuoteRepository])
void main() {
  late GetRandomQuote usecase;
  late MockQuoteRepository mockQuoteRepository;

  setUp(() {
    mockQuoteRepository = MockQuoteRepository();
    usecase = GetRandomQuote(mockQuoteRepository);
  });

  test('should get random quote from the repository', () async {
    // arrange
    final tQuote = Quote(
      text: 'Test quote',
      author: 'Test Author',
      category: 'motivation',
    );
    when(mockQuoteRepository.getRandomQuote()).thenAnswer((_) async => tQuote);

    // act
    final result = await usecase.execute();

    // assert
    expect(result, tQuote);
    verify(mockQuoteRepository.getRandomQuote());
    verifyNoMoreInteractions(mockQuoteRepository);
  });
}
